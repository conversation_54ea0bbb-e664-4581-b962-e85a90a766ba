package com.fxiaoke.open.erpsyncdata.custom.utils.tenant.Wensli;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.CategoryFieldDataMappingExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.service.SpecialSyncService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpProductCategory;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 2021/8/23 11:00
 * @description
 */

@Component
@Slf4j
public class WensliUtil {
    @Autowired
    private I18NStringManager i18NStringManager;


    @Autowired
    private SpecialSyncService specialSyncService;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private ProxyHttpClient proxyHttpClient;

    private static Long EXPIR_TIME = 2*60*60L;

    public enum TokenType{
        user,
        message
    }

    /**
     * @createTime 2021/8/23
     * @desc 存储产品分类
     * @param
     * @return 
     */
    public Result<String> saveCategory(String param,String tenantId){
        Map<String,Object> params = JacksonUtil.fromJson(param,Map.class);
        JSONObject jsonObject = JSON.parseObject(param);
        JSONArray array = jsonObject.getJSONArray("categoryList");
        System.out.println(array);
        if(null == array){
            return Result.newError("fail", I18NStringEnum.s3658 + param);
        }
        //产品分类数据
        List<ErpProductCategory> erpProductCategoryList = array.toJavaList(ErpProductCategory.class);
        //企信通知人员编号
        Integer userId = Integer.parseInt((String) params.get("userId"));
        //数据中心id
        String dataCenterId = (String) params.get("dataCenterId");

        System.out.println(tenantId+","+userId+","+dataCenterId);
        System.out.println(erpProductCategoryList);

        Result<List<CategoryFieldDataMappingExcelVo>> listResult = specialSyncService.syncErpProductCategory(tenantId, userId, dataCenterId, erpProductCategoryList,null);

        System.out.println(listResult);

        Integer categorySize = 0;
        if(listResult.getData() != null){
            categorySize = listResult.getData().size();
        }

        return Result.newSuccess(i18NStringManager.getByEi2(I18NStringEnum.s3709, tenantId, String.valueOf(categorySize)));
    }

    /**
     * @createTime 2021/8/23
     * @desc 获取token，6000s缓存，过期以refresh刷新
     * @param 	
     * @return 
     */
    public Result<String> getToken(String ei,TokenType tokenType){
        Map<String, String> getTokenParams = new HashMap(){{
            put("timestamp",System.currentTimeMillis());
        }};
        String secret;
        String accessTokenKey;
        String refreshTokenKey;
        //人员相关token
        if(TokenType.user == tokenType){
            String eid = "12249779";
            secret = "guLni0lrg00DzBfcXZFXJv7maqVfcmPs";
            accessTokenKey = "Wensli_OA_accessToken" + eid + secret;
            refreshTokenKey = "Wensli_OA_refreshToken" + eid + secret;
            getTokenParams.put("eid",eid);
            getTokenParams.put("scope","resGroupSecret");
        }else{  //消息相关token
            String appid = "500722924";
            secret = "02BtYyzXDVI7dSCxRj8y";
            accessTokenKey = "Wensli_OA_accessToken" + appid + secret;
            refreshTokenKey = "Wensli_OA_refreshToken" + appid + secret;
            getTokenParams.put("appid",appid);
            getTokenParams.put("scope","app");
        }
        try{
            String accessToken = redisDataSource.get(this.getClass().getSimpleName()).get(accessTokenKey);
            if(StringUtils.isEmpty(accessToken)){
                String refreshToken = redisDataSource.get(this.getClass().getSimpleName()).get(refreshTokenKey);
                String getTokenUrl;
                //没有生成过token或refresh过期
                if(StringUtils.isEmpty(refreshToken)){
                    getTokenUrl = "https://www.yunzhijia.com/gateway/oauth2/token/getAccessToken";
                    getTokenParams.put("secret",secret);
                }else{  //刷新token
                    getTokenUrl = "https://www.yunzhijia.com/gateway/oauth2/token/refreshToken";
                    getTokenParams.put("refreshToken",refreshToken);
                }
                HttpRspLimitLenUtil.ResponseBodyModel tokenResponse = proxyHttpClient.postUrl(getTokenUrl, getTokenParams, Collections.emptyMap(), (Long)null);
                log.info("Wensli token requestUrl={}, getTokenParams={}, response={}", getTokenUrl, getTokenParams, tokenResponse);
                JSONObject tokenJsonObject = JSONObject.parseObject(tokenResponse.getBody());
                if(!StringUtils.isEmpty(tokenResponse.getBody())){
                    Boolean tokenSuccess = tokenJsonObject.getBoolean("success");
                    if(!tokenSuccess){
                        log.warn("Wensli token fail, ei={}", ei);
                        return Result.newError(ResultCodeEnum.NO_USER);
                    }else{
                        JSONObject tokenData = tokenJsonObject.getJSONObject("data");
                        accessToken = tokenData.getString("accessToken");
                        refreshToken = tokenData.getString("refreshToken");
                    }
                }
                redisDataSource.get(this.getClass().getSimpleName()).set(accessTokenKey,accessToken,"NX","EX",6000);
                redisDataSource.get(this.getClass().getSimpleName()).set(refreshTokenKey,refreshToken,"NX","EX",EXPIR_TIME);
            }
            return Result.newSuccess(accessToken);
        }catch (Exception e){
            log.info("Wensli token exception: ", e);

            return Result.newError("fail", e.getMessage());
        }
    }
}
