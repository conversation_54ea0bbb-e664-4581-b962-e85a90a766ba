package com.fxiaoke.open.erpsyncdata.custom.utils.tenant.farben;

import com.facishare.converter.EIEAConverter;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import com.fxiaoke.open.erpsyncdata.custom.utils.tenant.CommonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.autoconf.ConfigFactory;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class FileUtils {


    @Autowired
    private StoneProxyApi stoneProxyApi;
    @Autowired
    EIEAConverter eieaConverter;

    public static String httpProxy;

    /**
     * 文件上传
     * @param paramsStr
     * @return
     * @throws IOException
     */
    public Result<String> uploadFile2Homs(String paramsStr) throws IOException {
        Map<String,Object> params =  GsonUtil.fromJson(paramsStr, HashMap.class);
        log.info("parmas:{}",params);
        ArrayList<LinkedTreeMap<String, String>> files = (ArrayList) params.get("files");

        Proxy proxy = CommonUtil.getProxy();
        OkHttpClient client = new OkHttpClient().newBuilder().proxy(proxy).build();
        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);

        builder.addFormDataPart("contractCode", (String) params.get("contractCode"));

        files.forEach(item -> {
            InputStream fileStream = null;
            File tempFile = null;
            try {
                fileStream = getFileStream(params, item);
                Path temp = Files.createTempFile("temp-", "."+item.get("ext"));
                Files.copy(fileStream, temp, StandardCopyOption.REPLACE_EXISTING);
                tempFile  = temp.toFile();
            } catch (FRestClientException e) {
                e.printStackTrace();
            }catch (IOException e) {
                e.printStackTrace();
            }

            builder.addFormDataPart("files",item.get("filename"),
                    RequestBody.create(MediaType.parse("application/octet-stream"),
                            tempFile ));
            builder.addFormDataPart("crmNPaths", (String)item.get("path"));
        });

        RequestBody body = builder.build();
        Request request = new Request.Builder()
                .url((String)params.get("url"))
                .method("POST", body)
                .addHeader("appSecretKey", (String) params.get("appSecretKey"))
                .build();

        Response response = client.newCall(request).execute();
        ResponseBody responseBody =  response.body();
        String result = responseBody.string();
        log.info(result);

        return Result.newSuccess(result);
    }


    private InputStream getFileStream(Map<String, Object> params, Map<String, String> fileItem) throws FRestClientException {
        StoneFileDownloadRequest stoneFileDownloadRequest = new StoneFileDownloadRequest();
        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt((String) params.get("tenantId")));
        stoneFileDownloadRequest.setEa(ea);
        stoneFileDownloadRequest.setPath(fileItem.get("path"));
        stoneFileDownloadRequest.setEmployeeId(Integer.parseInt((String) params.get("userId")));
        stoneFileDownloadRequest.setBusiness((String) params.get("business"));
        InputStream inputStream = stoneProxyApi.downloadStream(stoneFileDownloadRequest);
        return inputStream;
    }


}
