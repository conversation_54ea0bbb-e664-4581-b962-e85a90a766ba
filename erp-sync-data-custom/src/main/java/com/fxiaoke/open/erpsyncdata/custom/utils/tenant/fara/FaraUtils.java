package com.fxiaoke.open.erpsyncdata.custom.utils.tenant.fara;

import com.facishare.converter.EIEAConverter;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import com.fxiaoke.open.erpsyncdata.custom.utils.tenant.CommonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.autoconf.ConfigFactory;
import com.squareup.okhttp.OkHttpClient;
import io.openDocAPI.client.ApiClient;
import io.openDocAPI.client.ApiException;
import io.openDocAPI.client.Configuration;
import io.openDocAPI.client.api.DefaultApi;
import io.openDocAPI.client.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.InputStreamEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

@Component
@Slf4j
public class FaraUtils {
    private APIInstanceManager apiInstceManager;

    @Autowired
    private  StoneProxyApi stoneProxyApi;
    @Autowired
    EIEAConverter eieaConverter;

    public static String httpProxy;

    public static final String regexPattern = "(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d\\:([0-9]+))";

    /**
     * 附件上传
     * @param param
     * @return
     * @throws Exception
     */
    public Result<String> uploadFile(String param) throws Exception {
        Map<String,String> params =  GsonUtil.fromJson(param, HashMap.class);
        log.info("parmas:{}",params);

        LoginInfo loginInfo = new LoginInfo();
        loginInfo.setHostIp(params.get("hostIp"));
        loginInfo.setPort(Integer.parseInt(params.get("port")));
        loginInfo.setUserName(params.get("userName"));
        loginInfo.setPassword(params.get("password"));

        ApiClient defaultClient = new ApiClient();
        Proxy proxy = CommonUtil.getProxy();
        OkHttpClient okHttpClient = new OkHttpClient();
        okHttpClient.setProxy(proxy);
        okHttpClient.setConnectTimeout(30, TimeUnit.SECONDS);
        okHttpClient.setReadTimeout(30, TimeUnit.SECONDS);
        defaultClient.setHttpClient(okHttpClient);
        Configuration.setDefaultApiClient(defaultClient);

        //登录
        apiInstceManager = new APIInstanceManager(loginInfo);

        log.info("start upload...");
        //获取 Docid
        String docId = getDocidByPath(params.get("targetDir"));
        log.info("docId:{}",docId);

        FileOsbeginuploadReq fileOsbeginuploadReq = new FileOsbeginuploadReq();

        fileOsbeginuploadReq.setDocid(docId);
        fileOsbeginuploadReq.setOndup(2L);

        fileOsbeginuploadReq.setName(params.get("filename"));
        // 获取文件流
        InputStream fileStream = getFileStream(params);

        //the size of file get by custom function maybe wrong.
        // so copy fileInputStream into a temporary file,figure out the actual size.
        Path temp = Files.createTempFile("temp-", "."+params.get("ext"));
        Files.copy(fileStream, temp, StandardCopyOption.REPLACE_EXISTING);
        File tempfile  = temp.toFile();
        fileOsbeginuploadReq.setLength(tempfile.length());
        InputStream tempFileInputStream = new FileInputStream(tempfile);

        String gns  = singleUpload(fileOsbeginuploadReq,tempFileInputStream, params);

        return Result.newSuccess(gns);
    }

    private String singleUpload(FileOsbeginuploadReq fileOsbeginuploadReq, InputStream fileStream,Map<String, String> params) throws Exception {
        DefaultApi apiInstance = apiInstceManager.getAPIInstanceWithToken();
        // 开始上传文件协议
        FileOsbeginuploadRes osbeginuploadResult = apiInstance.fileOsbeginuploadPost(fileOsbeginuploadReq);
        log.info("osbeginuploadResult:{}",osbeginuploadResult);
        String docId = osbeginuploadResult.getDocid();
        // 根据服务器返回的对象存储请求，向对象存储上传数据
        InputStreamEntity body = new InputStreamEntity(fileStream,fileOsbeginuploadReq.getLength());
        Vector<String> headers = new Vector<String>();
        List<String> authRequestList = osbeginuploadResult.getAuthrequest();
        for (int i = 2; i < authRequestList.size(); ++i) {
            String header = authRequestList.get(i);
            headers.add(header);
        }
        String originUrl = authRequestList.get(1);
        if(StringUtils.isNotBlank(params.get("uploadUrl"))){
//            if(Pattern.matches(regexPattern,originUrl)){  // originUrl格式为：ip+端口,则替换成uploadUrl
//                originUrl = originUrl.replaceAll(regexPattern,params.get("uploadUrl"));
//            }else{   // originUrl格式为：ip+端口，则将uploadOriginUrl替换成uploadUrl
//                originUrl = originUrl.replaceAll(params.get("uploadOriginUrl"),params.get("uploadUrl"));
//            }
            originUrl = originUrl.replaceAll(regexPattern,params.get("uploadUrl"));
            if(StringUtils.isNotBlank(params.get("uploadOriginUrl"))){
                originUrl = originUrl.replaceAll(params.get("uploadOriginUrl"),params.get("uploadUrl"));
            }
        }
        OSSHelper ossHttpHelper = null;
        try {
            ossHttpHelper = new OSSHelper();
            ossHttpHelper.SendReqToOSS(authRequestList.get(0),originUrl , headers, body);
        }catch (Exception e){
            log.warn("upload file an exception occur!");
            e.printStackTrace();
        }finally {
            // 调用osendupload API
            FileOsenduploadReq osenduploadBody = new FileOsenduploadReq();
            osenduploadBody.setDocid(osbeginuploadResult.getDocid());
            osenduploadBody.setRev(osbeginuploadResult.getRev());
            //上传文件完成协议
            FileOsenduploadRes osenduploadResult = apiInstance.fileOsenduploadPost(osenduploadBody);
            log.info("osenduploadResult:{}",osenduploadResult);
            return docId;
        }
    }

    /**
     * 根据path下载文件
     * @param params
     * @return
     * @throws FRestClientException
     */
    private InputStream getFileStream(Map<String,String> params) throws FRestClientException {
        StoneFileDownloadRequest stoneFileDownloadRequest = new StoneFileDownloadRequest();
        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(params.get("tenantId")));
        stoneFileDownloadRequest.setEa(ea);
        stoneFileDownloadRequest.setPath(params.get("path"));
        stoneFileDownloadRequest.setEmployeeId(Integer.valueOf(params.get("userId")));
        stoneFileDownloadRequest.setBusiness(params.get("business"));
        InputStream inputStream = stoneProxyApi.downloadStream(stoneFileDownloadRequest);
        return inputStream;
    }

    /**
     * 通过云盘的路径获取docId
     * @param namePath
     * @return
     */
    private String getDocidByPath(String namePath) {
        DefaultApi apiInstance = apiInstceManager.getAPIInstanceWithoutToken();
        FileGetinfobypathReq getinfobypathBody = new FileGetinfobypathReq();
        getinfobypathBody.setNamepath(namePath);
        String docId = "";
        try {
            docId = apiInstance.fileGetinfobypathPost(getinfobypathBody).getDocid();
        } catch (ApiException e) {
            System.out.println(e.getCode() + " " + e.getResponseBody() + " " + e.getResponseHeaders());
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return docId;
    }
}
