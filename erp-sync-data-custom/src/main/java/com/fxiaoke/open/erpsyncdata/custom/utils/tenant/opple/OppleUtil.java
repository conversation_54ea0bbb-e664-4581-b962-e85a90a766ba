package com.fxiaoke.open.erpsyncdata.custom.utils.tenant.opple;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.custom.utils.tenant.CommonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.Md5Util;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.Proxy;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 2021/9/26 16:13
 * @description
 */
@Component
@Slf4j
public class OppleUtil {

    //private final static String BASE_URL = "https://qystest.opple.com:9182";
//    private final static String APP_TOKEN = "SSwXZMBQkB";
//    private final static String APP_SECRET = "LPZR9vu6EJz1RCVVSuzy060FsM1wZO";

    @Autowired
    private StoneProxyApi stoneProxyApi;
    @Autowired
    private EIEAConverter eieaConverter;

    public Result<String> createByFile(Map<String,String> param, String tenantId){

        String baseUrl=param.get("baseUrl");

        String appToken=param.get("appToken");

        String appSecret=param.get("appSecret");


        //创建合同文档
        String createFileUrl = baseUrl + "/v2/document/createbyfile";

        try{
            //下载CRM文件
            StoneFileDownloadRequest stoneFileDownloadRequest = new StoneFileDownloadRequest();

            log.info("opple file param:filePath={},userId={},bussiness={}",param.get("filePath"),param.get("userId"),param.get("bussiness"));

            String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
            stoneFileDownloadRequest.setEa(ea);
            stoneFileDownloadRequest.setPath(param.get("filePath"));
            stoneFileDownloadRequest.setEmployeeId(Integer.valueOf(param.get("userId")));
            stoneFileDownloadRequest.setBusiness(param.get("bussiness"));
            InputStream inputStream = stoneProxyApi.downloadStream(stoneFileDownloadRequest);

            //文件后缀名
            String buffix = StringUtils.substringAfterLast(param.get("filePath"),".");
            //构建临时文件
            Path temp = Files.createTempFile("temp-", "."+buffix);
            Files.copy(inputStream, temp, StandardCopyOption.REPLACE_EXISTING);
            File tempFile  = temp.toFile();
            Proxy proxy = CommonUtil.getProxy();
            OkHttpClient client = new OkHttpClient().newBuilder().proxy(proxy).build();
            MultipartBody.Builder builder = new MultipartBody.Builder();
            builder.setType(MultipartBody.FORM);
            builder.addFormDataPart("file", tempFile.getName(), RequestBody.create(MediaType.parse("application/octet-stream"), tempFile))
                    .addFormDataPart("fileType",buffix)
                    .addFormDataPart("title",param.get("title"));
            if(!StringUtils.isEmpty(param.get("waterMarks"))){
                builder.addFormDataPart("builder",param.get("waterMarks"));
            }
            RequestBody body = builder.build();
            //时间戳
            String timeStamp = System.currentTimeMillis()/1000 + "";
            //x-qys-signature生成规则为 32位的MD5（apptoken+app_secret+时间戳）
            String x_qys_signature = Md5Util.md5(appToken+appSecret+timeStamp);

            log.info("opple createbyfile header:x-qys-accesstoken={},x-qys-signature={},x-qys-timestamp={}",appToken,x_qys_signature,timeStamp);

            Request request = new Request.Builder()
                    .url(createFileUrl)
                    .method("POST", body)
                    .addHeader("x-qys-accesstoken",appToken)
                    .addHeader("x-qys-signature",x_qys_signature)
                    .addHeader("x-qys-timestamp",timeStamp)
                    .addHeader("Content-Type","multipart/form-data")
                    .build();
            Response response = client.newCall(request).execute();
            if(response.isSuccessful()){
                ResponseBody responseBody = response.body();
                JSONObject responseJson = JSONObject.parseObject(responseBody.string());
                int code = (int)responseJson.get("code");
                if(0 != code){
                    log.info("opple createbyfile fail:code={},message={}",responseJson.get("code"),responseJson.get("message"));
                    return Result.newError(ResultCodeEnum.CALL_OUT_HTTP_FAILED,responseJson.get("message").toString());
                }
                JSONObject resultJson = (JSONObject)responseJson.get("result");
                log.info("opple createbyfile result:{}",resultJson);
                return Result.newSuccess((String)resultJson.get("documentId"));
            }else{
                log.info("opple createbyfile fail:{}",response.message());
                return Result.newError(ResultCodeEnum.CALL_OUT_HTTP_FAILED,response.message());
            }
        }catch (Exception e){
            log.error("opple createbyfile fail:",e);
            return Result.newError(ResultCodeEnum.CALL_OUT_HTTP_FAILED,e.getMessage());
        }
    }

}
