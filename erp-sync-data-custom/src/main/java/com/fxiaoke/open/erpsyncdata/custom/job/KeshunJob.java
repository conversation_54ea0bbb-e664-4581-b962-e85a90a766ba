package com.fxiaoke.open.erpsyncdata.custom.job;

import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpSyncTimeDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by fengyh on 2021/2/2.
 */
@Service("KeshunJob")
@Slf4j
public class KeshunJob implements JobWorker {
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ErpSyncTimeDao erpSyncTimeDao;
    private static String AccountStatement="";

    private static String accountStatementApiName="";

    @Override
    public void doJob() {
//        Long currentTime = System.currentTimeMillis();
//        String tenantId="";//沙盒
//        Result<String> result=executeJob(tenantId,currentTime);
//        log.info("executeJob tenantId={} result={}",tenantId,result);
//        String tenantId1="";//正式
//        Result<String> result1=executeJob(tenantId1,currentTime);
//        log.info("executeJob tenantId={} result={}",tenantId1,result1);
    }
//    public Result<String> executeJob(String tenantId,Long currentTime){
//        ErpConnectInfoEntity connectInfo = erpConnectInfoDao.getByTenantId(tenantId);
//        if(connectInfo==null){
//            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
//        }
//        Result<String> result=getKeShunAccountStatement2pushTable(tenantId,connectInfo,currentTime);
//        return result;
//    }
//    public Result<String> getKeShunAccountStatement2pushTable(String tenantId,ErpConnectInfoEntity connectInfo,Long currentTime){
//        SearchTemplateQuery searchTemplateQuery=new SearchTemplateQuery();
//        searchTemplateQuery.addFilter("field_snaex__c", Lists.newArrayList("option1"), "EQ");
//        searchTemplateQuery.addFilter("field_b25i7__c", Lists.newArrayList(""), "ISN");
//        List<ObjectData> objectDataList=getCrmObjectData(tenantId, ObjectApiNameEnum.FS_ACCOUNT_OBJ.getObjApiName(),searchTemplateQuery);
//        if(CollectionUtils.isEmpty(objectDataList)){
//            return Result.newSuccess();
//        }
//        List<ErpSyncExtentDTO> erpSyncTimeExtentDTO = erpSyncTimeDao.listSyncExtentByTenantId(tenantId);
//        List<ErpSyncTimeVO> syncTimeVos = new ArrayList<>();
//        for (ErpSyncExtentDTO dto : erpSyncTimeExtentDTO) {
//            if (StringUtils.isNotBlank(AccountStatement)&&!AccountStatement.equals(dto.getObjectApiName())) {
//                //支持指定对象
//                continue;
//            }
//            SyncPloyDetailData ployDetail = dto.getSyncPloyDetailData();
//            //筛除未开启的事件
//            if (!ployDetail.getSyncRules().getEvents().contains(dto.getOperationType())){
//                continue;
//            }
//            ErpSyncTimeVO syncTime = new ErpSyncTimeVO();
//            BeanUtils.copyProperties(dto, syncTime);
//            syncTimeVos.add(syncTime);
//        }
//        if(CollectionUtils.isEmpty(syncTimeVos)){
//            return Result.newSuccess();
//        }
//        ErpSyncTimeVO erpSyncTimeVO=syncTimeVos.get(0);
//        TimeFilterArg timeFilterArg=new TimeFilterArg();
//        timeFilterArg.setTenantId(tenantId);
//        timeFilterArg.setObjAPIName(accountStatementApiName);
//        timeFilterArg.setOperationType(1);
//        timeFilterArg.setStartTime(erpSyncTimeVO.getLastSyncTime());
//        timeFilterArg.setEndTime(currentTime);
//        timeFilterArg.setIncludeDetail(true);
//        for(ObjectData objectData:objectDataList){
//            List<FilterData> filters=Lists.newArrayList();
//            FilterData customerFilter=new FilterData();
//            customerFilter.setFieldApiName("");
//            customerFilter.setFieldValue(Lists.newArrayList(String.valueOf(objectData.get(""))));
//            filters.add(customerFilter);
//            timeFilterArg.setFilters(filters);
//            List<StandardData> erpDataList=getErpObjectData(timeFilterArg, connectInfo);
//
//        }
//        ErpSyncTimeEntity arg = ErpSyncTimeEntity.builder().id(erpSyncTimeVO.getId()).lastSyncTime(currentTime).build();
//        int row = erpSyncTimeDao.updateByIdAdmin(arg);
//        log.info("updateByIdAdmin last sync time,erpSyncTime:{},row:{}", erpSyncTimeVO, row);
//        return Result.newSuccess();
//    }
//    public List<StandardData> getErpObjectData(TimeFilterArg timeFilterArg, ErpConnectInfoEntity connectInfo){
//        ErpChannelEnum channel = connectInfo.getChannel();
//        BaseErpDataManager erpDataManager = ConnectorHandlerFactory.getDataHandlerByChannel(channel);
//        Integer limit=100;
//        Integer offset=0;
//        List<StandardData> allDataList=Lists.newArrayList();
//        while (true){
//            timeFilterArg.setLimit(limit);
//            timeFilterArg.setOffset(offset);
//            List<StandardData> dataList=Lists.newArrayList();
//            Result<StandardListData> result=erpDataManager.listErpObjDataByTime(timeFilterArg,connectInfo.getConnectParams());
//            if (result != null && result.isSuccess() && result.getData() != null && result.getData().getDataList() != null) {
//                dataList=result.getData().getDataList();
//            }else{
//                log.info("objectDataService.queryBySearchTemplate timeFilterArg={}, connectInfo={}，result={}",timeFilterArg,connectInfo,result);
//            }
//            allDataList.addAll(dataList);
//            if(dataList.size()!=limit){
//                break;
//            }
//            offset+=limit;
//        }
//        return allDataList;
//    }
//    public List<ObjectData> getCrmObjectData(String tenantId, String objApiName, SearchTemplateQuery searchTemplateQuery){
//        Integer limit=100;
//        Integer offset=0;
//        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), CrmConstants.SYSTEM_USER);
//        List<ObjectData> allDataList=Lists.newArrayList();
//        while (true){
//            searchTemplateQuery.setLimit(limit);
//            searchTemplateQuery.setOffset(offset);
//            List<ObjectData> dataList=Lists.newArrayList();
//            com.fxiaoke.crmrestapi.common.result.Result<QueryBySearchTemplateResult> crmDataResult = objectDataService.queryBySearchTemplate(headerObj, objApiName, searchTemplateQuery);
//            if (crmDataResult != null && crmDataResult.isSuccess() && crmDataResult.getData() != null && crmDataResult.getData().getQueryResult() != null
//                    && crmDataResult.getData().getQueryResult().getData()!=null) {
//                dataList=crmDataResult.getData().getQueryResult().getData();
//            }else{
//                log.info("objectDataService.queryBySearchTemplate tenantId={}, objApiName={}, SearchTemplateQuery={},result={}",tenantId,objApiName,searchTemplateQuery,crmDataResult);
//            }
//            allDataList.addAll(dataList);
//            if(dataList.size()!=limit){
//                break;
//            }
//            offset+=limit;
//        }
//        return allDataList;
//    }
}
