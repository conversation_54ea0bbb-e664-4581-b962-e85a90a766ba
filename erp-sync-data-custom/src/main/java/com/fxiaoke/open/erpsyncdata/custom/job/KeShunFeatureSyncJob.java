package com.fxiaoke.open.erpsyncdata.custom.job;

import com.fxiaoke.open.erpsyncdata.custom.service.FeatureSyncService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFeatureSyncDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/5/22
 */

@Slf4j
@EnableScheduling
@Component
public class KeShunFeatureSyncJob {
    @Autowired
    private ErpFeatureSyncDao erpFeatureSyncDao;
    @Autowired
    private FeatureSyncService featureSyncService;

    @Scheduled(cron = "0 0 0/1 * * ?")
    public void execute(){
        List<String> tenantIds = erpFeatureSyncDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId)).queryDistinctTenantId();
        log.info("execute sync job prepare,tenantIDs:{}",tenantIds);
        for (String tenantId : tenantIds) {
            log.info("execute sync job begin，tenantId：{}",tenantId);
            Result<String> result = featureSyncService.executeTenantFeatureSync(tenantId, "job");
            log.info("execute sync job end，tenantId：{},result:{}",tenantId,result);
        }
    }
}
