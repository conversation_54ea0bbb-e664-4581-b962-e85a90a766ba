package com.fxiaoke.open.erpsyncdata.custom.arg;

import com.fxiaoke.crmrestapi.common.data.Filter;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 11:19 2021/2/4
 * @Desc:
 */
@Data
public class GetErpData2TableArg implements Serializable {
    private CrmDataFilter crmDataFilter;
    private ErpDataFilter erpDataFilter;

    @Data
    public static class CrmDataFilter implements Serializable {
        private String objApiName;
        private List<Filter> filters;
        private Map<String,String> erpField2CrmField;
    }
    @Data
    public static class ErpDataFilter implements Serializable {
        private String erpFakeObjApiName;
        private Long startTime;
        private Long endTime;
        private List<Map<String,String>> erpField2Values;
    }
}
