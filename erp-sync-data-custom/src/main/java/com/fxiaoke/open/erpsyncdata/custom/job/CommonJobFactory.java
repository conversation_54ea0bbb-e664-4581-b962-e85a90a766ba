package com.fxiaoke.open.erpsyncdata.custom.job;

import com.google.common.collect.Maps;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * Created by fengyh on 2021/2/2.
 */

@Component
public class CommonJobFactory implements ApplicationContextAware {
    private ApplicationContext applicationContext;
    private Map<String, ? extends JobWorker> jobWorkerMap = Maps.newHashMap();


    @PostConstruct
    private void init() {
        Map<String, ? extends JobWorker> jobWorkerMap = applicationContext.getBeansOfType(JobWorker.class);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public Map<String, ? extends JobWorker> getWorkerMap() {
        return jobWorkerMap;
    }

}