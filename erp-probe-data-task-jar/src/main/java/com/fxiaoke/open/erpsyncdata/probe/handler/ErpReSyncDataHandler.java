package com.fxiaoke.open.erpsyncdata.probe.handler;

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.probe.service.BatchTenantJobService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.*;

import static cn.hutool.core.thread.ThreadUtil.sleep;

/**
 * <AUTHOR>
 * @Date: 10:10 2022/6/9
 * @Desc:
 */
@Slf4j
@Component
@JobHander(value = "erpReSyncDataHandler")
public class ErpReSyncDataHandler  extends IJobHandler {
    @Autowired
    private BatchTenantJobService batchTenantJobService;
    @Autowired
    private SyncPloyDetailSnapshotDao syncPloyDetailSnapshotDao;

    private static ExecutorService  executorService;
    static {
        ThreadFactory workerFactory = new ThreadFactoryBuilder()
                .setNameFormat("ErpReSyncDataJob-%d").build();
        executorService = new ThreadPoolExecutor(10, 30, 0, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(), workerFactory);
    }

    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        executeJob(triggerParam);
        return ReturnT.SUCCESS;
    }

    public void executeJob(TriggerParam triggerParam) {
        //依赖数据重试、socketTimeOut重试、，只处理有开启快照的企业
        List<String> allTenantIds = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId)).listEnableSnapshotTenantId();
        for (String tenantId : allTenantIds) {
            executorService.submit(()->{
                TraceUtil.initTraceWithFormat(tenantId);
                TraceUtil.addChildTrace("reSyncDataJob");
                batchTenantJobService.reSyncDataExecuteSingleTenantTasks(tenantId);
            });
        }
    }
}
