package com.fxiaoke.open.erpsyncdata.probe.service.impl;

import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.probe.service.CleanSyncDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 清理SyncData表中无用数据
 *
 * <AUTHOR>
 * @date 2021/5/27
 */
@Slf4j
@Service("cleanSyncDataService")
public class CleanSyncDataServiceImpl implements CleanSyncDataService {


    @Override
    public Result<String> executeTasks(String tenantId) {
//        log.info("executeTasks start,tenantId={}", tenantId);
//        // 只清理一个月前的快照
//        Long time = System.currentTimeMillis() - ConfigCenter.CLEAN_SYNC_TIME_RANGE;
//        List<SyncPloyDetailEntity> syncPloyDetailEntityList = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
//                .listByTenantIdAndStatus(tenantId, null);
//        for (SyncPloyDetailEntity syncPloyDetailEntity : syncPloyDetailEntityList) {
//            log.debug("executeTasks objApi start,objApiName={}", syncPloyDetailEntity.getSourceObjectApiName());
//            DetailObjectMappingsData detailObjectMappingsData = syncPloyDetailEntity.getDetailObjectMappings();
//            List<Map<String, String>> objApiNameMap = Lists.newArrayList();
//            Map<String, String> map = Maps.newHashMap();
//            map.put("source", syncPloyDetailEntity.getSourceObjectApiName());
//            map.put("dest", syncPloyDetailEntity.getDestObjectApiName());
//            objApiNameMap.add(map);
//            if (detailObjectMappingsData != null) {
//                for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : detailObjectMappingsData) {
//                    map = Maps.newHashMap();
//                    map.put("source", detailObjectMappingData.getSourceObjectApiName());
//                    map.put("dest", detailObjectMappingData.getDestObjectApiName());
//                    objApiNameMap.add(map);
//                }
//            }
//            for (Map<String, String> objMap : objApiNameMap) {
//                try {
//                    List<String> allDataIds = adminSyncDataDao.setTenantId(tenantId).getAllDataIdByObjectApi(tenantId, objMap.get("source"),
//                            objMap.get("dest"));
//                    log.debug("cleanSyncData count:{},obj:{},ei:{}", allDataIds.size(), objMap, tenantId);
//                    for (String dataId : allDataIds) {
//                        if (!isBelong()) {
//                            log.info("cleanSyncData task sleep.tenantId={},objName={}", tenantId, objMap.get("source"));
//                            try {
//                                Thread.sleep(3600000);
//                            } catch (InterruptedException e) {
//                                log.error("sleep error.", e);
//                            }
//                        }
//                        // 查询需要删除数据
//                        List<SyncDataEntity> syncDataEntityList = adminSyncDataDao.setTenantId(tenantId).queryNeedDeleteData(tenantId, objMap.get("source"), dataId,
//                                objMap.get("dest"), time);
//                        if(syncDataEntityList != null && syncDataEntityList.size()!=0){
//                            // 删除数据
//                            List<String> ids = syncDataEntityList.stream().map(v -> v.getId()).collect(Collectors.toList());
//                            adminSyncDataDao.setTenantId(tenantId).deleteByIds(tenantId,ids);
//                        }
//                    }
//                } catch (Throwable e) {
//                    log.error("CleanSyncDataJob task error.objApiName = {}", objMap, e);
//                }
//            }
//            log.info("executeTasks objApi end,objApiName={}", syncPloyDetailEntity.getSourceObjectApiName());
//        }
//        log.info("executeTasks end,tenantId={}", tenantId);
        return Result.newSuccess();
    }


    private boolean isBelong() {
        SimpleDateFormat dateFormater = new SimpleDateFormat("HHmm");
        String date = dateFormater.format(new Date());
        System.out.println(date);
        int time = Integer.parseInt(date);
        if (time < 800 || time > 2000) {
            return true;
        } else {
            return false;
        }
    }


}
