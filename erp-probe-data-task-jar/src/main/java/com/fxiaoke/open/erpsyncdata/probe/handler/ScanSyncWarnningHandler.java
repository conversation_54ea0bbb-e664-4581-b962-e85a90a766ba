package com.fxiaoke.open.erpsyncdata.probe.handler;


import com.fxiaoke.common.concurrent.DynamicExecutors;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CountSyncDataAndSendArg;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ProbeErpDataService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ScanSyncWarnningService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 10:10 2022/6/9
 * @Desc:
 */
@Slf4j
@Component
@JobHander(value = "scanSyncWarnningHandler")
public class ScanSyncWarnningHandler extends IJobHandler {
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ScanSyncWarnningService scanSyncWarnningService;
    @Autowired
    private ProbeErpDataService probeErpDataService;
    @Autowired
    @Lazy
    private ConfigCenterConfig configCenterConfig;

    private static ExecutorService executorService= Executors.newFixedThreadPool(10);

    private static ThreadPoolExecutor countSyncDataExecutor = DynamicExecutors.newThreadPool(0, 30, 10 * 1000, new ThreadFactoryBuilder().setNameFormat("countSyncDataHandler-%d").build());
    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        executeJob(triggerParam);
        return ReturnT.SUCCESS;
    }

    public void executeJob(TriggerParam triggerParam) {
        log.info("execute job,triggerParam:{}", triggerParam);
//        //运行在本作业服务器的分片序列号
//        int shardingItem = triggerParam.getBroadcastIndex();
//        //分片总数
//        int shardingTotalCount = triggerParam.getBroadcastTotal();
        Set<String> vipTenantIds = tenantConfigurationManager.getVipTenantIds();
        List<String>tenantIds=new ArrayList<>();
        tenantIds.addAll(vipTenantIds);
//        //筛选出匹配分片的企业
//        tenantIds.removeIf(ei -> !matchSharding(shardingItem, shardingTotalCount, ei));
        for (String tenantId : vipTenantIds) {
            executorService.execute(()->{
                scanSyncWarnningService.scanSyncDataMappingErrorNumber(tenantId);
            });
        }

        /**
         * 计算1天企业集成流同步数量
         */
        CountSyncData();
    }

    private void CountSyncData() {
        log.info("CountSyncData start");

        final Map<String, Integer> countPloySyncData = configCenterConfig.getCountPloySyncData();
        // 获取当天0点的时间戳
        LocalDate today = LocalDate.now();
        ZonedDateTime todayMidnight = today.atStartOfDay(ZoneId.systemDefault());
        long timestamp = todayMidnight.toEpochSecond() * 1000;

        final Map<Integer, Pair<Long, Long>> timeMap = countPloySyncData.values().stream()
                .distinct()
                .collect(Collectors.toMap(Function.identity(), i -> Pair.of(timestamp - 1000L * 3600 * 24 * i, timestamp - 1000L * 3600 * 24 * (i - 1))));

        countPloySyncData.forEach((tenantId, minusDay) -> {
            final Pair<Long, Long> timePair = timeMap.get(minusDay);
            final Long startTime = timePair.getKey();
            final Long endTime = timePair.getValue();
            countSyncDataExecutor.submit(() -> probeErpDataService.countSyncDataAndSend(new CountSyncDataAndSendArg(tenantId, startTime, endTime)));
        });
    }

    private boolean matchSharding(int index, int total, String tenantId) {
        long longId = getLong(tenantId);
        return longId % total == index;
    }

    private long getLong(String tenantId) {
        long longId;
        try {
            longId = Long.parseLong(tenantId);
        } catch (NumberFormatException e) {
            log.warn("this tenantId is not long type,tenantId:{}", tenantId, e);
            longId = tenantId.hashCode();
        }
        return longId;
    }

}
