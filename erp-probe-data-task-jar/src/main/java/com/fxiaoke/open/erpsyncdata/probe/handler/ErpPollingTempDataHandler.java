package com.fxiaoke.open.erpsyncdata.probe.handler;

import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ParallelUtils;
import com.fxiaoke.open.erpsyncdata.probe.service.ProbeDataTaskService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 17:40 2022/8/3
 * @Desc:
 */
@Slf4j
@Component
@JobHander(value = "erpPollingTempDataHandler")
public class ErpPollingTempDataHandler extends IJobHandler {
    @Autowired
    private ProbeDataTaskService probeDataTaskService;
    @Autowired
    private SyncPloyDetailSnapshotDao syncPloyDetailSnapshotDao;
    /**
     * 执行任务
     *
     * @param triggerParam
     */
    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        executeJob(triggerParam);
        return ReturnT.SUCCESS;
    }
    public void executeJob(TriggerParam triggerParam) {
        log.info("execute job,triggerParam:{}", triggerParam);
        //只处理有开启快照的企业
        List<String> allTenantIds = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId)).listEnableSnapshotTenantId();
        List<String> tenantIds=allTenantIds.stream().distinct().collect(Collectors.toList());//去重
        //后台任务,查询mongo库获取需要轮询的erp数据
        ParallelUtils.ParallelTask rollingErpDataFromMongoTask = ParallelUtils.createRollingErpDataFromMongoTask();
        for (String tenantId : tenantIds) {
            rollingErpDataFromMongoTask.submit(()-> probeDataTaskService.executeRollingErpDataFromMongoJob(tenantId));
        }
        rollingErpDataFromMongoTask.run();
    }

}
