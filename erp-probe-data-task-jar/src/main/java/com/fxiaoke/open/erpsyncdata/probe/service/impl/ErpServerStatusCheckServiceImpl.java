package com.fxiaoke.open.erpsyncdata.probe.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.MonitorUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.vo.TenantCheckStatusMsg;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpServerStatusResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.probe.manger.TaskProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.probe.service.ErpServerStatusCheckService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service("erpServerStatusCheckServiceImpl")
public class ErpServerStatusCheckServiceImpl implements ErpServerStatusCheckService {

    @Autowired
    private RedisDataSource redisDataSource;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ConfigCenterConfig configCenterConfig;

    @Autowired
    private TaskProxyHttpClient proxyHttpClient;

    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    @Qualifier("notificationService")
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private I18NStringManager i18NStringManager;

    private final String CHECK_SERVER_STATUAS = "check_server_status_%s";

    @Override
    public Result<Void> executeCheckErpServerStatus(String tenantId) {
        //#1
        String status = redisDataSource.get(this.getClass().getSimpleName()).get(String.format("check_server_status_%s", tenantId));
        if (StringUtils.isNotEmpty(status) && status.contains("all")) {
            log.debug("tenantId :{} server check  stoping");
            return Result.newSuccess();
        }
        TraceUtil.initTraceWithFormat(tenantId);

        //获取锁
        boolean getEiLock = false;
        String lockName = "redis_lock_check_server_status" + tenantId;
        try {
            getEiLock = redissonClient.getLock(lockName).tryLock(3, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.info("get redission lock for ei:{} get exception", e);
        }
        if (!getEiLock) {
            return Result.newError(ResultCodeEnum.GET_LOCK_FAILED);
        }

        //check
        try {
            log.info("{}:checkErpServerStatus start", tenantId);
            checkErpServerStatus(tenantId);
            log.info("{}:checkErpServerStatus end", tenantId);
        } catch (Throwable t) {
            log.warn("process RollingErpDataServerStatus ByTenantId execute Ploys Throwable", t);
        } finally {
            log.info("trace probe RollingErpDataServerStatus redission lock for ei:{}, relese lock", tenantId);
            redissonClient.getLock(lockName).unlock();
        }

        return Result.newSuccess();
    }

    public Result<Void> checkErpServerStatus(String tenantId) {

        //获取当前企业的探测url
        ErpTenantConfigurationEntity configurationEntity = new ErpTenantConfigurationEntity();
        configurationEntity.setTenantId(tenantId);
        configurationEntity.setType(TenantConfigurationTypeEnum.TENANT_SERVER_CHECK_URL.name());

        List<ErpTenantConfigurationEntity>
                serverCheckUrls = tenantConfigurationManager.queryList(tenantId, configurationEntity);

        Integer errorLimit = configCenterConfig.getErpServerStatusErrorLimit(tenantId);

        String redisKey = String.format(CHECK_SERVER_STATUAS, tenantId);
        String hvals = redisDataSource.get(this.getClass().getSimpleName()).get(redisKey);
        List<String> unHealthDataCenterIds;
        if (StringUtils.isEmpty(hvals)) {
            unHealthDataCenterIds = Lists.newArrayList();
        } else {
            unHealthDataCenterIds = JacksonUtil.fromJson(hvals, List.class);
        }

        if (unHealthDataCenterIds.size() == serverCheckUrls.size()) {
            log.debug("暂停中");
            return Result.newSuccess();
        }
        for (ErpTenantConfigurationEntity entity : serverCheckUrls) {
            String checkUrl = entity.getConfiguration();
            if (unHealthDataCenterIds.contains(checkUrl)) {
                log.debug("url:{} is breaked", checkUrl);
                //当前URL已经被熔断了。则不需要再次探测。等redis缓存过期后再进行接口探测
                continue;
            }
            //调用服务接口，由于在task模块直接调用。所以要求ERP开放的接口是无需权限认证的。
            Result<ErpServerStatusResult> erpServerStatus = executorCheckServerUrl(tenantId,checkUrl);
            send2Monitor(tenantId, checkUrl, erpServerStatus.getErrMsg(), erpServerStatus.isSuccess());
            if (!erpServerStatus.isSuccess() || erpServerStatus.getData() == null) {
                //代理服务器检测到的错误超过阈值则预警
                Integer erpServerCheckInterval = configCenterConfig.getErpServerCheckInterval(tenantId);
                String message = i18NStringManager.getByEi2(I18NStringEnum.s1026.getI18nKey(),
                        tenantId,
                        String.format(I18NStringEnum.s1026.getI18nValue(), tenantId, checkUrl,erpServerCheckInterval),
                        Lists.newArrayList(tenantId, checkUrl,erpServerCheckInterval+""));
                sendMessage(tenantId, entity.getDataCenterId(),
                        i18NStringManager.getByEi(I18NStringEnum.s1025,tenantId)
                        , message);
                unHealthDataCenterIds.add(checkUrl);
                continue;


            }

            Integer errorCount = erpServerStatus.getData().getErrorCount();
            //超过3分钟上报的错误量按照每分钟错误数据量进行评估
            if (System.currentTimeMillis() - erpServerStatus.getData().getLastUpTime() > 3 * 60 * 1000) {
                Integer minute = Math.toIntExact(
                        (System.currentTimeMillis() - erpServerStatus.getData().getLastUpTime()) / (60 * 1000));
                errorCount = errorCount / minute;
            }
            if (errorCount > errorLimit) {
                //代理服务器检测到的错误超过阈值则预警
                sendMessage(tenantId, entity.getDataCenterId(), i18NStringManager.getByEi(I18NStringEnum.s1025,tenantId),
                        i18NStringManager.getByEi2(I18NStringEnum.s1027.getI18nKey(),
                                tenantId,
                                String.format(I18NStringEnum.s1027.getI18nValue(), tenantId, checkUrl, configCenterConfig.getErpServerCheckInterval(tenantId), errorCount, errorLimit, erpServerStatus.getData().getErrorCount()),
                                Lists.newArrayList(tenantId, checkUrl, configCenterConfig.getErpServerCheckInterval(tenantId)+"", errorCount+"", errorLimit+"", erpServerStatus.getData().getErrorCount()+"")));
                unHealthDataCenterIds.add(checkUrl);
                continue;
            }

        }

        if (unHealthDataCenterIds.size() > 0) {
            redisDataSource.get(this.getClass().getSimpleName()).set(redisKey, JacksonUtil.toJson(unHealthDataCenterIds));
            //按照分钟级别缓存
            redisDataSource.get(this.getClass().getSimpleName()).expire(redisKey, 60L * configCenterConfig.getErpServerCheckInterval(tenantId));
        }

        return Result.newSuccess();

    }

    private void send2Monitor(String tenantId, String checkUrl, String errMsg, boolean success) {
        TenantCheckStatusMsg checkStatusMsg = TenantCheckStatusMsg.builder()
                .tenantId(tenantId)
                .checkUrl(checkUrl)
                .msg(errMsg)
                .isSuccess(success)
                .createTime(System.currentTimeMillis())
                .build();
        MonitorUtil.send(checkStatusMsg, MonitorType.TENANT_CHECK_STATUS);
    }


    public Result<ErpServerStatusResult> executorCheckServerUrl(String tenantId,String url) {
        Result<ErpServerStatusResult> result;
        String response =null;
        try {
            log.info("requestUrl:{}", url);
            response = proxyHttpClient.getUrl(url, Collections.emptyMap());
            if("success".equals(response)){//u8/eai/db的测试接口
                ErpServerStatusResult statusResult=new ErpServerStatusResult();
                statusResult.setLastUpTime(System.currentTimeMillis());
                statusResult.setErrorCount(0);
                return Result.newSuccess(statusResult);
            }
            log.info("response:{}", response);
            JSONObject resultObj = JSONObject.parseObject(response);
            if (resultObj != null && "0".equals(resultObj.get("errorCode").toString())) {
                String data = resultObj.getString("data");
                ErpServerStatusResult statusResult =
                        JacksonUtil.fromJson(data, new TypeReference<ErpServerStatusResult>() {
                        });
                return Result.newSuccess(statusResult);
            } else if (resultObj != null && resultObj.get("errorCode") != null) {
                return Result.newError(resultObj.get("errorMessage").toString());
            } else {
                return Result.newError(i18NStringManager.getByEi(I18NStringEnum.s36,tenantId));
            }
        } catch (Exception e) {
            log.warn("请求监测地址异常:", e);
            if(StringUtils.isNotBlank(response)){
                result = Result.newError(response);
            }else{
                result = Result.newError(e.getMessage());
            }

        }
        return result;
    }


    private void sendMessage(String tenantId, String dcId, String title, String message) {
        log.info("sendMessage tenant:{},message:{}");
        SendAdminNoticeArg arg = SendAdminNoticeArg.builder()
                .msg(message)
                .msgTitle(title)
                .tenantId(tenantId)
                .dcId(dcId)
                .alwaysSendSuperAdmin(true)
                .sendSuperAdminIfNoSendTenantAdmin(false)
                .needFillPreDbName(false)
                .build();
        arg = arg.addTraceInfo();
        notificationService.sendTenantAdminNotice(arg,
                AlarmRuleType.GENERAL,
                AlarmRuleType.GENERAL.getName(i18NStringManager,null,tenantId),
                AlarmType.POLLING_ERP_API_EXCEPTION,
                AlarmLevel.URGENT);
    }

}
