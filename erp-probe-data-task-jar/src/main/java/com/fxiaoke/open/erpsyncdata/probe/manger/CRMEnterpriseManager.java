package com.fxiaoke.open.erpsyncdata.probe.manger;

import com.alibaba.fastjson.JSONObject;
import com.facishare.uc.api.event.EnterpriseRunStatusEvent;
import com.facishare.uc.api.model.enterprise.arg.BatchGetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.EnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CRM企业管理器类
 * <AUTHOR>
 * @date 2022-06-23
 */
@Component
@Slf4j
public class CRMEnterpriseManager {
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;

    private static final String KEY_EI_LIST_CACHE="KEY_EI_LIST_CACHE";

    /**
     * 获取运行中的企业ID，默认缓存1天
     * @param eiList
     * @return
     */
    public List<Integer> getRunningEnterpriseIdList(List<Integer> eiList) {
        List<Integer> runningEnterpriseIdList = new ArrayList<>();

        //如果存在缓存，刚使用缓存数据
        String value = redisDataSource.get(this.getClass().getSimpleName()).get(KEY_EI_LIST_CACHE);
        if(StringUtils.isNotEmpty(value)) {
            runningEnterpriseIdList = JSONObject.parseArray(value,Integer.class);
            return runningEnterpriseIdList;
        }
        BatchGetEnterpriseDataArg arg = new BatchGetEnterpriseDataArg();
        arg.setEnterpriseIds(eiList);
        BatchGetEnterpriseDataResult result = enterpriseEditionService.batchGetEnterpriseData(arg);

        if(result!=null && CollectionUtils.isNotEmpty(result.getEnterpriseDatas())) {
            for(EnterpriseData enterpriseData : result.getEnterpriseDatas()) {
                if(enterpriseData.getRunStatus()== EnterpriseRunStatusEvent.RUN_STATUS_NORMAL) {
                    runningEnterpriseIdList.add(enterpriseData.getEnterpriseId());
                } else {
                    erpConnectInfoManager.deleteByTenantId(enterpriseData.getEnterpriseId()+"");
                    log.info("CRMEnterpriseManager.getRunningEnterpriseId,CRM enterprise without normal status,ei={},runStatus={}",
                            enterpriseData.getEnterpriseId(),enterpriseData.getRunStatus());
                }
            }
        }

        if(CollectionUtils.isEmpty(runningEnterpriseIdList)) {
            runningEnterpriseIdList = eiList;
        }

        //缓存转换后的数据
        redisDataSource.get(this.getClass().getSimpleName()).set(KEY_EI_LIST_CACHE, JSONObject.toJSONString(runningEnterpriseIdList));
        redisDataSource.get(this.getClass().getSimpleName()).expire(KEY_EI_LIST_CACHE,24 * 3600L);//默认缓存1天

        return runningEnterpriseIdList;
    }

    public List<String> getRunningEnterpriseIdList2(List<String> eiList) {
        List<Integer> enterpriseIdList = new ArrayList<>();
        for(String ei : eiList) {
            try {
                enterpriseIdList.add(Integer.valueOf(ei));
            } catch (Exception e){
                log.info("CRMEnterpriseManager.getRunningEnterpriseIdList2,ei={} invalid",ei);
            }
        }

        List<Integer> runningEnterpriseIdList = getRunningEnterpriseIdList(enterpriseIdList);

        List<String> eis = new ArrayList<>();
        for(Integer ei : runningEnterpriseIdList) {
            eis.add(ei+"");
        }

        return eis;
    }

    public List<String> getRunningEnterpriseIdListByPage(List<String> eiList) {
        log.info("CRMEnterpriseManager.getRunningEnterpriseIdListByPage,eiList={}",eiList);
        //先排序
        eiList = eiList.stream().sorted().collect(Collectors.toList());

        //再分页
        List<List<String>> pageList = PageUtils.getPageList(eiList,100);

        List<String> runningEnterpriseList = new ArrayList<>();

        for(List<String> page : pageList) {
            List<String> tempList = getRunningEnterpriseIdList2(page);
            runningEnterpriseList.addAll(tempList);
        }

        log.info("CRMEnterpriseManager.getRunningEnterpriseIdListByPage,runningEnterpriseList={}",runningEnterpriseList);
        return runningEnterpriseList;
    }
}
