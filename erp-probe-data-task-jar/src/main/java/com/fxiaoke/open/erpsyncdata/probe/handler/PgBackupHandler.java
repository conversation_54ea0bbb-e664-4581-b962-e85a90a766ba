package com.fxiaoke.open.erpsyncdata.probe.handler;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.Week;
import cn.hutool.db.Db;
import cn.hutool.db.meta.Column;
import cn.hutool.db.meta.MetaUtil;
import cn.hutool.db.meta.Table;
import com.github.mybatis.local.TenantThreadLocal;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用于112环境 备份部分表
 * 每天一次日备份，每周一次周备份
 * 备份时，没有表就建表，有表就先清空备份表数据。
 *
 * <AUTHOR> (^_−)☆
 * @date 2023-08-10
 */
@Component
@JobHander(value = "pgBackupHandler")
@Slf4j
public class PgBackupHandler extends IJobHandler {
    @Autowired
    @Qualifier("hikariDataSource")
    private DataSource dataSource;

    private static final String DAY_BACKUP_TABLE_PREFIX = "bak_d_";
    private static final String WEEK_BACKUP_TABLE_PREFIX = "bak_w_";

    public List<String> getTableNeedBackup() {
        return ListUtil.of(
                "erp_connect_info",
                "erp_db_proxy_config",
                "erp_field_extend",
                "erp_object",
                "erp_object_field",
                "erp_object_relationship",
                "erp_tenant_configuration",
                "erp_u8_eai_config",
                "sync_ploy",
                "sync_ploy_detail"
        );
    }

    @Override
    public ReturnT<String> execute(TriggerParam triggerParam) throws Exception {
        Db db = new Db(dataSource) {
            @Override
            public Connection getConnection() throws SQLException {
                //setTenantId
                TenantThreadLocal.set("-10001");
                return super.getConnection();
            }
        };
        //日备份
        List<String> tables = getTableNeedBackup();
        for (String table : tables) {
            String bakTable = DAY_BACKUP_TABLE_PREFIX + table;
            backupTable(db, table, bakTable);
        }
        //周备份
        if (DateTime.now().dayOfWeekEnum().equals(Week.SUNDAY)) {
            for (String table : tables) {
                String bakTable = WEEK_BACKUP_TABLE_PREFIX + table;
                backupTable(db, table, bakTable);
            }
        }
        return ReturnT.SUCCESS;
    }

    private void backupTable(Db db, String sourceTable, String bakTable) {
        log.info("backup table begin from {} to {}", sourceTable, bakTable);
        try {
            Table sourceTableMeta = getTableMeta(sourceTable);
            Table targetTableMeta = getTableMeta(bakTable);
            if (targetTableMeta.getColumns().isEmpty()) {
                //表不存在,创建，仅复制结构
                String copyTableQuery = "CREATE TABLE " + bakTable + " AS SELECT * FROM " + sourceTable + " WHERE FALSE;";
                db.execute(copyTableQuery);
            } else {
                // 对比表结构并添加缺少的字段
                compareAndAddColumns(db, sourceTableMeta, targetTableMeta);
                // 清空目标表数据
                String truncateQuery = "TRUNCATE TABLE " + bakTable;
                db.execute(truncateQuery);
            }
            String cols = sourceTableMeta.getColumns().stream().map(Column::getName).collect(Collectors.joining(", "));
            String copySql = String.format("INSERT INTO %s (%s) SELECT %s FROM %s;", bakTable, cols, cols, sourceTable);
            executeSql(db, copySql);
        } catch (Exception e) {
            log.error("backup table error", e);
        } finally {
            log.info("backup table end from {} to {}", sourceTable, bakTable);
        }
    }

    private static void executeSql(Db db, String copySql) throws SQLException {
        //原始sql执行，不然会很慢
        try (Connection connection = db.getConnection()) {
            PreparedStatement preparedStatement = connection.prepareStatement(copySql);
            preparedStatement.execute();
        }
    }

    private Table getTableMeta(String sourceTable) {
        TenantThreadLocal.set("-10001");
        return MetaUtil.getTableMeta(dataSource, sourceTable);
    }

    private static void compareAndAddColumns(Db db, Table sourceTableMeta, Table targetTableMeta) throws SQLException {
        // 获取源表和目标表的列信息
        Collection<Column> sourceColumns = sourceTableMeta.getColumns();
        Collection<Column> targetColumns = targetTableMeta.getColumns();
        // 找出目标表缺少的列
        List<Column> missingColumns = new ArrayList<>();
        for (Column sourceColumn : sourceColumns) {
            boolean columnExists = false;
            for (Column targetColumn : targetColumns) {
                if (sourceColumn.getName().equalsIgnoreCase(targetColumn.getName())) {
                    columnExists = true;
                    break;
                }
            }
            if (!columnExists) {
                missingColumns.add(sourceColumn);
            }
        }
        String targetTable = targetTableMeta.getTableName();
        // 构造ALTER TABLE语句，用于在目标表中新增缺少的列
        for (Column missingColumn : missingColumns) {
            String alterSql = "ALTER TABLE " + targetTable + " ADD COLUMN " + missingColumn.getName() + " " + missingColumn.getTypeName();
            db.execute(alterSql);
        }
    }
}
