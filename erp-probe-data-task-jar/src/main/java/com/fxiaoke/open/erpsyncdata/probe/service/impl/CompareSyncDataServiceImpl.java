package com.fxiaoke.open.erpsyncdata.probe.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.CompareResultDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.factory.CompareSyncDataMongoDao;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.probe.service.CompareSyncDataService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CompareSyncDataServiceImpl  implements CompareSyncDataService {
    @Autowired
    private CompareSyncDataMongoDao compareSyncDataMongoDao;

    @Override
    public Result<Void> compareSyncData()  {
        String sourceTenantId="88466";
        String destTenantId="89180";

        List<CompareResultDoc> compareResultDocs = compareSyncDataMongoDao.listByTenantIdAndTypeAndStatusAndUpdateTime(sourceTenantId, 0, 100);
        while (CollectionUtils.isNotEmpty(compareResultDocs)){
            for (CompareResultDoc compareResultDoc : compareResultDocs) {
                List<CompareResultDoc> compareResultDocs1 = compareSyncDataMongoDao.listByTenantIdAndTypeAndStatusAndUpdateTime(destTenantId, compareResultDoc);
                if(CollectionUtils.isNotEmpty(compareResultDocs1)){
                    CompareResultDoc compareResultDoc1=compareResultDocs1.get(0);
                    SyncDataContextEvent sourceInputContext = JSONObject.parseObject(JSONObject.toJSONString(compareResultDoc.getInputParamsMap()), SyncDataContextEvent.class);
                    SyncDataContextEvent compareInputContext = JSONObject.parseObject(JSONObject.toJSONString(compareResultDoc1.getInputParamsMap()), SyncDataContextEvent.class);
                    SyncDataContextEvent sourceOutPutContext = JSONObject.parseObject(JSONObject.toJSONString(compareResultDoc.getOutputParamsMap()), SyncDataContextEvent.class);
                    SyncDataContextEvent compareOutPutContext = JSONObject.parseObject(JSONObject.toJSONString(compareResultDoc1.getOutputParamsMap()), SyncDataContextEvent.class);
                    compareResultDoc.getInputParamsMap();
                    Map<String, List<String>> compareDiffMap= Maps.newHashMap();
                    for (Field field : SyncDataContextEvent.class.getDeclaredFields()) {
                        field.setAccessible(true);
                        Map<String, List<String>> stringListMap = compareObjByType(compareDiffMap,field, sourceInputContext, sourceOutPutContext, compareInputContext, compareOutPutContext);
                        compareDiffMap.putAll(stringListMap);
                    }
                    if(compareDiffMap.size()>0){
                        log.info("compareSyncData found diff sourceObject:{}.destObject:{}",JSONObject.toJSONString(compareResultDoc),JSONObject.toJSONString(compareResultDocs1));
                        compareResultDoc.setHasCompare(true);
                        compareResultDoc1.setHasCompare(true);
                        compareResultDoc.setTheSame(false);
                        compareResultDoc1.setTheSame(false);
                        compareResultDoc.setCompareField(compareDiffMap);
                        compareResultDoc1.setCompareField(compareDiffMap);
                    }else {
                        log.info("compareSyncData found not diff: sourceDataId:{} destObjId:{}",compareResultDoc.getId(),compareResultDocs1.get(0).getId());
                        compareResultDoc.setHasCompare(true);
                        compareResultDoc1.setHasCompare(true);
                        compareResultDoc.setTheSame(true);
                        compareResultDoc1.setTheSame(true);
                    }
                    compareSyncDataMongoDao.batchInsert(sourceTenantId,Lists.newArrayList(compareResultDoc));
                    compareSyncDataMongoDao.batchInsert(destTenantId,Lists.newArrayList(compareResultDoc1));

                }

            }
        }
        return null;
    }

    private Map<String, List<String>> compareObjByType(Map<String, List<String>> compareDiffMap, Field fieldObj, Object sourceInputObj, Object sourceOutObj, Object compareInputObj, Object compareOutObj)  {
        try {
            if(!fieldObj.getType().isPrimitive()&&!wrapperBaseType(fieldObj.getType())&&false){//先跳过
                for (Field field : fieldObj.getClass().getDeclaredFields()) {
                    try {
                        field.setAccessible(true);
                        Class<?> fieldType = field.getType();
                        //
                        Object sourceInputValue=field.get(sourceInputObj);
                        Object compareInputValue=field.get(compareInputObj);
                        Object sourceOutputValue=field.get(sourceOutObj);
                        Object compareOutputValue=field.get(compareOutObj);
                        if (!fieldType.isPrimitive()&&!wrapperBaseType(fieldType)) {
                            compareObjByType(compareDiffMap,field, sourceInputValue, sourceOutputValue, compareInputValue, compareOutputValue);
                            return compareDiffMap;
                        }
                        //比对两个值是否都为空或者都不为空。
                        compareValue(field.getName(),sourceInputValue,compareInputValue,compareDiffMap,"input");
                        compareValue(field.getName(),sourceOutputValue,compareOutputValue,compareDiffMap,"output");
                    } catch (Exception e) {
                        System.out.println(field);
                    }
                }
            }else{
                fieldObj.setAccessible(true);
                Object sourceInputValue=fieldObj.get(sourceInputObj);
                Object compareInputValue=fieldObj.get(compareInputObj);
                Object sourceOutputValue=fieldObj.get(sourceOutObj);
                Object compareOutputValue=fieldObj.get(compareOutObj);
                //比对两个值是否都为空或者都不为空。
                compareValue(fieldObj.getName(),sourceInputValue,compareInputValue,compareDiffMap,"input");
                compareValue(fieldObj.getName(),sourceOutputValue,compareOutputValue,compareDiffMap,"output");
            }


        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return compareDiffMap;
    }
    private boolean wrapperBaseType(Class<?> fieldType){

        return fieldType == Integer.class ||
                fieldType == Long.class ||
                fieldType == Float.class ||
                fieldType == Double.class ||
                fieldType == Boolean.class ||
                fieldType == Character.class ||
                fieldType == Byte.class ||
                fieldType == Short.class||
                fieldType == ObjectData.class||
                fieldType == String.class;

    }

    private Boolean compareValue(String fieldName,Object sourceValue,Object destValue,Map<String,List<String>> maps,String type){
        if(ObjectUtils.isNotEmpty(sourceValue)&&ObjectUtils.isNotEmpty(destValue)){
            return true;
        }else if(ObjectUtils.isEmpty(sourceValue)&&ObjectUtils.isEmpty(destValue)){
            return true;
        }
        maps.computeIfAbsent(type,key -> Lists.newArrayList()).add(fieldName);
        System.out.println("compareValue :sourceValue:"+sourceValue+" destValue"+destValue);
        return false;
    }
}
