package com.fxiaoke.open.erpsyncdata.common.util;

import com.facishare.converter.EIEAConverter;

public  class SandboxUtil {
    public static boolean isNotSandbox(EIEAConverter converter, String tenantId) {
        try {
            String ea = converter.enterpriseIdToAccount(Integer.valueOf(tenantId));
            return !ea.endsWith("sandbox");
        }catch (Exception e) {
            return false;
        }
    }
}
