package com.fxiaoke.open.erpsyncdata.common.constant;

public interface Operate {
    // filter使用的操作符 start
    String IN = "IN";
    /**
     * is null  为空
     */
    String IS = "IS";
    /**
     * 不等于
     */
    String IS_NOT = "ISN";
    String LIKE = "LIKE";
    String NOT_LIKE = "NLIKE";
    /**
     * 自定义查询条件
     */
    String CUSTOM = "CUSTOM";
    // filter使用的操作符 end

    String EQUALS = "equals";
    String NOT_EQUALS = "notequals";
    /**
     * 包含
     */
    String CONTAINS = "contains";
    /**
     * 不包含
     */
    String NOT_CONTAINS = "notcontains";
    /**
     * 以..开始
     */
    String START_WITH_CAPITAL = "STARTWITH";
    /**
     * 以..开始
     */
    String START_WITH = "startwith";
    /**
     * 以..结尾,小写的之前就存在了，不知道有没有用
     */
    String END_WITH_CAPITAL = "ENDWITH";
    /**
     * 以..结尾
     */
    String END_WITH = "endwith";
    String BETWEEN_CAPITAL = "BETWEEN";
    String BETWEEN = "between";
    String NOT_IN = "NIN";
    String NOT_BETWEEN = "notbetween";
    String NOT_START_WITH = "notstartwith";
    String NOT_END_WITH = "notendwith";
    String GT = "GT";
    String LT = "LT";
    String GTE = "GTE";
    String LTE = "LTE";
    String EQ = "EQ";
    String N = "N";
}
