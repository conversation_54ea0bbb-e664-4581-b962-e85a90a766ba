package com.fxiaoke.open.erpsyncdata.common.rule;

import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import java.util.List;
import java.util.stream.Collectors;

public class ConditionUtil {
    public static String getTrueExpression() {
        return "true";
    }

    public static String getFalseExpression() {
        return "false";
    }

    public static String parseToOrExpression(List<List<FilterData>> rulesWithOr) {
        if (rulesWithOr == null || rulesWithOr.isEmpty()) {
            return getTrueExpression();
        }
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < rulesWithOr.size(); i++) {
            if (i != 0) {
                builder.append(" || ");
            }
            List<FilterData> rules = rulesWithOr.get(i);
            builder.append("(");
            String expression = parseToAndExpression(rules);
            builder.append(expression);
            builder.append(")");
        }
        return builder.toString();
    }

    private static String parseToAndExpression(List<FilterData> rules) {
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < rules.size(); i++) {
            if (i != 0) {
                builder.append(" && ");
            }
            FilterData rule = rules.get(i);
            builder.append("(");
            String expression = parseExpression(rule);
            builder.append(expression);
            builder.append(")");
        }
        return builder.toString();
    }

    public static String parseExpression(FilterData rule) {
        String fieldType = rule.getFieldType();
        if (rule.getFieldType().equals(FieldType.QUOTE) || rule.getFieldType().equals(FieldType.FORMULA)) {
            fieldType = rule.getQuoteFieldType();
        }

        if (rule.getFieldType().equals(FieldType.OBJECT_REFERENCE)) {
            fieldType = rule.getQuoteFieldType();
        }

        if (FieldType.BOOL.equals(fieldType)&&rule.getFieldValue().size()>0){
            String v = String.valueOf(rule.getFieldValue().get(0));
            if (!(v.equalsIgnoreCase("true")||v.equalsIgnoreCase("false"))){
                //bool类型传入的不是布尔类型值
                fieldType= FieldType.STRING;
            }
        }
        List<String> fieldValues = (List<String>) rule.getFieldValue().stream().map(value -> String.valueOf(value)).collect(Collectors.toList());
        return OperateUtil.operateExpressionMap(rule.getFieldApiName(), fieldType, rule.getOperate(), fieldValues);
    }
}
