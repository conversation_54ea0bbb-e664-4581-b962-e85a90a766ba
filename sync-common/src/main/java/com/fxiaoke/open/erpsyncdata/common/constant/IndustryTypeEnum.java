//package com.fxiaoke.open.erpsyncdata.common.constant;
//
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//
///**
// * 各行业类型
// */
//@Getter
//@AllArgsConstructor
//public enum IndustryTypeEnum {
//    MANUFACTURE(1, "manufacture", "制造行业", "syncdata_common_industrytypeenum_manufacture"),
//    FAST_MOVING(2, "fast_moving","快消行业", "syncdata_common_industrytypeenum_fast_moving"),
//    HIGH_TECH_INTERNET(3, "high_tech_internet","高科互联网行业", "syncdata_common_industrytypeenum_high_tech_internet");
//
//    private Integer type;
//    private String name;
//    private String desc;
//    private String i18nKey;
//
//    public static String getName(Integer type) {
//        for (IndustryTypeEnum enumTemp : IndustryTypeEnum.values()) {
//            if (enumTemp.type == type) {
//                return enumTemp.name;
//            }
//        }
//        return null;
//    }
//    public static IndustryTypeEnum getIndustryTypeEnum(String name) {
//        for (IndustryTypeEnum enumTemp : IndustryTypeEnum.values()) {
//            if (enumTemp.name.equals(name)) {
//                return enumTemp;
//            }
//        }
//        return null;
//    }
//}
