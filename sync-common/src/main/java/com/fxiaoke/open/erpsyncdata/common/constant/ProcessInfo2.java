package com.fxiaoke.open.erpsyncdata.common.constant;

import com.github.autoconf.helper.ConfigHelper;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022-11-24
 */
@Slf4j
public class ProcessInfo2 {
    /**
     * 有环境，如fs-erp-sync-data-gray
     */
    public static String appName;
    /**
     *
     */
    public static String serverIp;
    /**
     *
     */
    public static String profile;

    static {
        //赋值
        try {
            appName = ConfigHelper.getProcessInfo().getName();
            serverIp = ConfigHelper.getProcessInfo().getIp();
            profile = ConfigHelper.getProcessInfo().getProfile();
        } catch (Throwable th) {
            log.warn("ProcessInfo2 init error", th);
        }
    }
}
