package com.fxiaoke.open.erpsyncdata.common.util;

import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import com.google.common.collect.Lists;
import java.util.Date;

public class FieldTypeUtil {
    public static boolean isStringType(String fieldType) {
        if (FieldType.getStringTypeList().contains(fieldType)){
            return true;
        }
        return false;
    }

    public static boolean isBooleanType(String fieldType) {
        if (FieldType.getBooleanFieldTypeList().contains(fieldType)){
            return true;
        }
        return false;
    }

    public static boolean isListStringType(String fieldType) {
        if (FieldType.getListStringTypeList().contains(fieldType)){
            return true;
        }
        return false;
    }

    public static boolean isNumberType(String fieldType) {
        if (FieldType.getStringTypeList().contains(fieldType)){
            return true;
        }
        return false;
    }

    public static Object convertByFieldType(Object object, String fieldType) {
        if (object == null) {
            return null;
        }
        Object result = object;
        if (FieldTypeUtil.isNumberType(fieldType)) {
            result = FieldTypeUtil.convertNumberObject(object);
        } else if (FieldTypeUtil.isStringType(fieldType)) {
            result = FieldTypeUtil.convertStringObject(object);
        } else if (FieldTypeUtil.isListStringType(fieldType)) {
            result = FieldTypeUtil.convertListStringObject(object);
        }
        return result;
    }

    private static Object convertListStringObject(Object object) {
        Object result = FieldTypeUtil.convertStringObject(object);
        if (!(result instanceof String)){
            return object;
        }
        return Lists.newArrayList(result);
    }


    private static Object convertStringObject(Object object) {
        if (object instanceof String) {
            return object;
        } else if (object instanceof Integer) {
            return String.valueOf(object);
        } else if (object instanceof Long) {
            return String.valueOf(object);
        }
        return object;
    }

    private static Object convertNumberObject(Object object) {
        if (object instanceof Long) {
            return object;
        } else if (object instanceof Date) {
            return ((Date) object).getTime();
        } else if (object instanceof String) {
            return Long.valueOf((String) object);
        } else if (object instanceof Integer) {
            return Long.valueOf((Integer) object);
        }
        return object;
    }
}
