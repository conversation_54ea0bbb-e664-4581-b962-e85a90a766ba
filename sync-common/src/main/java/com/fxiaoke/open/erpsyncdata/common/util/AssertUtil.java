package com.fxiaoke.open.erpsyncdata.common.util;

import com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.common.exception.SyncDataException;

public class AssertUtil {
    public static void notNull(String argument, Object obj) {
        if (obj == null) {
            throw new SyncDataException(ResultCodeEnum.PARAM_ERROR.getErrCode(), argument + "is null");
        }
        if (obj instanceof String && ((String) obj).isEmpty()) {
            throw new SyncDataException(ResultCodeEnum.PARAM_ERROR.getErrCode(), argument + "is empty");
        }
    }

    public static void assertIfEmpty(Object... objects) {
        for (Object o : objects) {
            notNull(o.toString(), o);
        }
    }
}
