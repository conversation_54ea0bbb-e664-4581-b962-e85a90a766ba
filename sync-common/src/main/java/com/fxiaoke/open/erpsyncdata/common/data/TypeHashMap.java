package com.fxiaoke.open.erpsyncdata.common.data;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

public class TypeHashMap<K, V> extends LinkedHashMap<K, V> {
    public BigDecimal getBigDecimal(String key) {
        Object value = this.get(key);
        if (value == null) {
            return null;
        } else if (value.getClass() == BigDecimal.class) {
            return (BigDecimal) value;
        } else if (value.getClass() == Double.class) {
            return new BigDecimal((Double) value);
        } else {
            return value.getClass() == Double.TYPE ? new BigDecimal((Double) value) : new BigDecimal(value.toString());
        }
    }

    public Long getLong(String key) {
        Object value = this.get(key);
        if (value == null) {
            return null;
        } else if (value.getClass() == Long.class) {
            return (Long) value;
        } else if (value.getClass() == Long.TYPE) {
            return (Long) value;
        } else if (value.getClass() == Double.class) {
            return ((Double) value).longValue();
        } else if (value.getClass() == Double.TYPE) {
            return Double.valueOf((Double) value).longValue();
        } else {
            String valueString = value.toString();
            if (Strings.isNullOrEmpty(valueString)) {
                return null;
            } else {
                if (valueString.contains("E")) {
                    BigDecimal bigDecimal = new BigDecimal(valueString);
                    valueString = bigDecimal.toPlainString();
                }

                return Long.valueOf(valueString);
            }
        }
    }

    public Integer getInt(String key) {
        Object value = this.get(key);
        if (value == null) {
            return null;
        } else if (value.getClass() == Integer.class) {
            return (Integer) value;
        } else if (value.getClass() == Long.TYPE) {
            return (Integer) value;
        } else if (value.getClass() == Double.class) {
            return ((Double) value).intValue();
        } else if (value.getClass() == Double.TYPE) {
            return Double.valueOf((Double) value).intValue();
        } else {
            String valueString = value.toString();
            if (Strings.isNullOrEmpty(valueString)) {
                return null;
            } else {
                if (valueString.contains("E")) {
                    BigDecimal bigDecimal = new BigDecimal(valueString);
                    valueString = bigDecimal.toPlainString();
                }

                return Integer.valueOf(valueString);
            }
        }
    }

    public String getString(String key) {
        Object value = this.get(key);
        if (value == null) {
            return null;
        } else {
            return value.getClass() == String.class ? (String) value : String.valueOf(value);
        }
    }



    public Boolean getBoolean(String key) {
        Object value = this.get(key);
        if (value == null) {
            return null;
        } else if (value.getClass() == Boolean.class) {
            return (Boolean) value;
        } else {
            return value.getClass() == Boolean.TYPE ? (Boolean) value : Boolean.valueOf(value.toString());
        }
    }

    public Double getDouble(String key) {
        Object value = this.get(key);
        if (value == null) {
            return null;
        } else if (value.getClass() == Double.class) {
            return (Double) value;
        } else {
            return value.getClass() == Double.TYPE ? (Double) value : Double.valueOf(value.toString());
        }
    }

    public Map<String, Object> getMap(String key) {
        Object value = this.get(key);
        if (value == null) {
            return null;
        } else {
            return value.getClass() == JSONObject.class ? ((JSONObject) value).getInnerMap() : (Map) value;
        }
    }


}
