package com.fxiaoke.open.erpsyncdata.common.rule;

import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import com.fxiaoke.open.erpsyncdata.common.constant.Operate;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FilterData implements Serializable {
    /**
     * 字段apiName
     */
    @ApiModelProperty("字段apiName")
    private String fieldApiName;
    /**
     * 枚举
     *
     * @see FieldType
     */
    @ApiModelProperty("字段类型")
    private String fieldType;
    @ApiModelProperty("字段标签")
    private String label;
    /**
     * 枚举
     *
     * @see Operate
     */
    @ApiModelProperty("操作符，IS ISN IN等")
    private String operate;
    @ApiModelProperty("字段值")
    private List fieldValue;
    /**
     * quote field type * 当fieldType == 'quote' 时，该字段表示字段的真正类型 当fieldType == 'formula'时，该字段表示计算字段的返回值类型
     */
    @ApiModelProperty("该字段表示字段的真正类型 当fieldType == 'formula'时，该字段表示计算字段的返回值类型")
    private String quoteFieldType;

    /**
     * 引用的对象字段（查找关联类型）
     */
    private String quoteRealField;
    /**
     * 引用目标对象
     */
    private String quoteFieldTargetObjectApiName;
    /**
     * 引用目标对象的字段
     */
    private String quoteFieldTargetObjectField;
    @ApiModelProperty("类型,判断是否是变量,传variable表示为变量类型字段")
    private String type;
    @ApiModelProperty("变量类型,判断是哪种变量类型字段")
    private String variableType;
    @ApiModelProperty("是否是between，且需要替换开始结束时间")
    @Builder.Default
    private Boolean isVariableBetween=false;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"fieldApiName\":\"")
                .append(fieldApiName).append('\"');
        sb.append(",\"fieldType\":\"")
                .append(fieldType).append('\"');
        sb.append(",\"label\":\"")
                .append(label).append('\"');
        sb.append(",\"operate\":\"")
                .append(operate).append('\"');
        sb.append(",\"fieldValue\":")
                .append(fieldValue);
        sb.append(",\"quoteFieldType\":\"")
                .append(quoteFieldType).append('\"');
        sb.append(",\"quoteRealField\":\"")
                .append(quoteRealField).append('\"');
        sb.append(",\"quoteFieldTargetObjectApiName\":\"")
                .append(quoteFieldTargetObjectApiName).append('\"');
        sb.append(",\"quoteFieldTargetObjectField\":\"")
                .append(quoteFieldTargetObjectField).append('\"');
        sb.append(",\"type\":\"")
                .append(type).append('\"');
        sb.append(",\"variableType\":\"")
                .append(variableType).append('\"');
        sb.append(",\"isVariableBetween\":")
                .append(isVariableBetween);
        sb.append('}');
        return sb.toString();
    }
}
