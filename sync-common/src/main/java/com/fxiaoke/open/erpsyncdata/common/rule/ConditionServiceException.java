package com.fxiaoke.open.erpsyncdata.common.rule;

import com.fxiaoke.open.erpsyncdata.common.constant.ConditionExceptionCodeEnum;
import com.github.trace.aop.IgnorableException;

/**
 * business exception class
 *
 * <AUTHOR>
 */
public class ConditionServiceException extends RuntimeException implements IgnorableException {
    private int code = -1;
    private String message;
    private Object[] paras;
    private boolean needReport;
    private Throwable cause = this;

    public int getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    public Object[] getParas() {
        return paras;
    }

    public ConditionServiceException(int code) {
        this.code = code;
        this.message = new StringBuilder("error code: [ ").append(code).append(" ]").toString();
    }

    public ConditionServiceException(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public ConditionServiceException(int code, Object... paras) {
        this.code = code;
        this.paras = paras;
        this.message = new StringBuilder("error code: [ ").append(code).append(" ]").toString();
    }

    public ConditionServiceException(int code, Throwable cause, Object... paras) {
        fillInStackTrace();
        this.code = code;
        this.paras = paras;
        this.message = new StringBuilder("error code: [ ").append(code).append(" ]").toString();
        this.cause = cause;
    }

    public ConditionServiceException(String message) {
        this.message = message;
    }

    public ConditionServiceException(Throwable cause) {
        fillInStackTrace();
        this.message = (cause == null ? null : cause.getMessage());
        this.cause = cause;
    }

    public ConditionServiceException(String message, Throwable cause) {
        fillInStackTrace();
        this.message = message;
        this.cause = cause;
    }

    public ConditionServiceException(ConditionExceptionCodeEnum exceptionMessage, String appendMessage) {
        this.code = exceptionMessage.getCode();
        this.message = exceptionMessage.getMessage() + ":" + appendMessage;
    }

    public ConditionServiceException(ConditionExceptionCodeEnum exceptionMessage, boolean needReport) {
        this.code = exceptionMessage.getCode();
        this.message = exceptionMessage.getMessage();
        this.needReport = needReport;
    }

    public ConditionServiceException(ConditionExceptionCodeEnum exceptionMessage, Throwable cause, boolean needReport) {
        fillInStackTrace();
        this.code = exceptionMessage.getCode();
        this.message = exceptionMessage.getMessage();
        this.cause = cause;
        this.needReport = needReport;
    }

    @Override
    public Throwable getCause() {
        return (cause == this ? null : cause);
    }

    public boolean isNeedReport() {
        return needReport;
    }

    public void setNeedReport(boolean needReport) {
        this.needReport = needReport;
    }

    @Override
    public String getErrorCode() {
        return String.valueOf(code);
    }
}
