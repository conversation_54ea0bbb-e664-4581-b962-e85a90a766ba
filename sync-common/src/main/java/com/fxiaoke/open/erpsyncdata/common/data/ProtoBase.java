package com.fxiaoke.open.erpsyncdata.common.data;

import io.protostuff.LinkedBuffer;
import io.protostuff.ProtobufIOUtil;
import io.protostuff.Schema;
import io.protostuff.runtime.RuntimeSchema;
import java.io.Serializable;

public class ProtoBase implements Serializable {

    public byte[] toProto() {
        Schema schema = RuntimeSchema.getSchema(getClass());
        return ProtobufIOUtil.toByteArray(this, schema, LinkedBuffer.allocate(256));
    }

    public void fromProto(byte[] bytes) {
        Schema schema = RuntimeSchema.getSchema(getClass());
        ProtobufIOUtil.mergeFrom(bytes, this, schema);
    }

}
