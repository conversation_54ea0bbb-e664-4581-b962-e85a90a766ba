package com.fxiaoke.open.erpsyncdata.common.util;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

/**
 * 参考 hutool ServletUtil
 *
 * <AUTHOR> (^_−)☆
 */
public class ServletUtil {
    public static String TLSv1 = "TLSv1";
    public static String TLSv11 = "TLSv1.1";

    /**
     * 忽略大小写获得请求header中的信息
     *
     * @param request        请求对象{@link HttpServletRequest}
     * @param nameIgnoreCase 忽略大小写头信息的KEY
     * @return header值
     */
    public static String getHeaderIgnoreCase(HttpServletRequest request, String nameIgnoreCase) {
        final Enumeration<String> names = request.getHeaderNames();
        String name;
        while (names.hasMoreElements()) {
            name = names.nextElement();
            if (name != null && name.equalsIgnoreCase(nameIgnoreCase)) {
                return request.getHeader(name);
            }
        }
        return null;
    }

    /**
     * 判断是否 明确指定 TLSv1 或者TLSv1.1
     *
     * @param request
     * @return
     */
    public static boolean isTlsV10OrV11(HttpServletRequest request) {
        String tlsVersion = getHeaderIgnoreCase(request, "X-TLS-Version");
        if (tlsVersion == null) {
            return false;
        }
        return TLSv1.equals(tlsVersion) || TLSv11.equals(tlsVersion);
    }
}
