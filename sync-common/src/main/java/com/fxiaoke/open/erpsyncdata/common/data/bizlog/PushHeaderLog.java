package com.fxiaoke.open.erpsyncdata.common.data.bizlog;

import io.protostuff.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PushHeaderLog {
    @Tag(1)
    @Builder.Default
    private String logType = "erpdss-push-header";
    @Tag(2)
    private long stamp;
    @Tag(3)
    private String appName="erpdss";
    @Tag(7)
    private String tenantId;
    @Tag(10)
    private String objectApiName;
    @Tag(11)
    private String objectId;
    //string1
    @Tag(51)
    private String xrealIp;
    @Tag(52)
    private String version;
    @Tag(53)
    private String dataCenterId;
    @Tag(54)
    private String dataCenterNumber;
    @Tag(55)
    private String operationType;
    @Tag(56)
    private String tlsVersion;
    @Tag(201)
    private long contentLength;
}
