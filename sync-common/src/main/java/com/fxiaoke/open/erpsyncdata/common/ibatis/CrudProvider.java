package com.fxiaoke.open.erpsyncdata.common.ibatis;

import com.github.mybatis.annotation.DynamicTypeHandler;
import com.github.mybatis.handler.list.ListTypeHandler;
import com.github.mybatis.handler.set.SetTypeHandler;
import com.github.mybatis.util.EntityUtil;
import com.github.mybatis.util.PersistMeta;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.sql.SQLException;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.util.CollectionUtils;

/**
 * Crud模板
 */
@Slf4j
public class CrudProvider {
    public static final String CLASS_KEY = "clazz";
    public static final String PARA_KEY = "para";
    public static final String VALUE_KEY = "value";
    public static final String PAGE_KEY = "page";
    public static final String COLUMN_KEY = "column";
    public static final String WHERE_KEY = "where";
    public static final String ORDER_KEY = "order";
    public static final String GROUP_KEY = "groupBy";
    //使用不可见字符
    public static final char FIELD_LEFT = '"';
    //使用不可见字符
    public static final char FIELD_RIGHT = '"';

    /**
     * 根据主键查找记录
     */
    public String get(final Map<String, Object> parameter) {
        Class<?> clazz = (Class<?>) parameter.get(CLASS_KEY);
        PersistMeta meta = EntityUtil.getMeta(clazz);
        return new SQL().SELECT("*").FROM(meta.getTableName()).WHERE(meta.getIdColumnName() + "=#{" + PARA_KEY + '}').toString();
    }

    /**
     * 查询所有记录(分页)
     */
    public String findByPage(final Map<String, Object> map) {
        Class<?> clazz = (Class<?>) map.get(CLASS_KEY);
        String names = map.containsKey(COLUMN_KEY) ? map.get(COLUMN_KEY).toString() : "*";
        SQL sql = new SQL().SELECT(names).FROM(EntityUtil.getTableName(clazz));
        if (map.containsKey(WHERE_KEY)) {
            Object obj = map.get(WHERE_KEY);
            if (obj != null && !Strings.isNullOrEmpty(obj.toString())) {
                sql.WHERE((String) obj);
            }
        }
        if (map.containsKey(ORDER_KEY)) {
            Object obj = map.get(ORDER_KEY);
            if (obj != null && !Strings.isNullOrEmpty(obj.toString())) {
                sql.ORDER_BY((String) map.get(ORDER_KEY));
            }
        }
        if (map.containsKey(GROUP_KEY)) {
            Object obj = map.get(GROUP_KEY);
            if (obj != null && !Strings.isNullOrEmpty(obj.toString())) {
                sql.GROUP_BY((String) map.get(GROUP_KEY));
            }
        }
        return sql.toString();
    }

    /**
     * 根据主键删除记录
     */
    public String deleteById(final Map<String, Object> parameter) {
        Class<?> clazz = (Class<?>) parameter.get(CLASS_KEY);
        PersistMeta meta = EntityUtil.getMeta(clazz);
        return new SQL().DELETE_FROM(meta.getTableName()).WHERE(meta.getIdColumnName() + "=#{" + PARA_KEY + '}').toString();
    }

    /**
     * 根据主键集合删除记录
     */
    public String deleteByIds(final Map<String, Object> parameter) throws SQLException {
        List<?> ids = (List<?>) parameter.get(PARA_KEY);
        if (null == ids || ids.isEmpty()) {
            throw new SQLException(PARA_KEY + " is null or empty");
        }
        Class<?> clazz = (Class<?>) parameter.get(CLASS_KEY);
        PersistMeta meta = EntityUtil.getMeta(clazz);
        String where;
        if (ids.iterator().next() instanceof Number) {
            where = meta.getIdColumnName() + " in (" + Joiner.on(",").join(ids) + ')';
        } else {
            where = meta.getIdColumnName() + " in ('" + Joiner.on("','").join(ids.stream().map(id -> StringEscapeUtils.escapeSql(String.valueOf(id))).collect(Collectors.toList())) + "')";
        }
        return new SQL().DELETE_FROM(meta.getTableName()).WHERE(where).toString();
    }

    /**
     * 新增操作
     *
     * @return String
     */
    public String insert(Object obj) {
        PersistMeta meta = EntityUtil.getMeta(obj.getClass());
        StringBuilder names = new StringBuilder(), values = new StringBuilder();
        int i = 0;
        for (Map.Entry<String, Field> kv : meta.getColumns().entrySet()) {
            Field field = kv.getValue();
            if (isNull(field, obj)) {
                continue;
            }

            if (i++ != 0) {
                names.append(',');
                values.append(',');
            }

            names.append(FIELD_LEFT).append(kv.getKey()).append(FIELD_RIGHT);
            DynamicTypeHandler typeHandler = field.getAnnotation(DynamicTypeHandler.class);
            if (typeHandler == null) {
                values.append("#{").append(field.getName()).append('}');
            } else {
                values.append("#{").append(field.getName()).append(",typeHandler=").append(typeHandler.value()).append('}');
            }
        }

        return new SQL().INSERT_INTO(getTableName(meta, obj)).VALUES(names.toString(), values.toString()).toString();
    }

    public String insertIgnore(Object obj) {
        return insert(obj) + " ON CONFLICT DO NOTHING";
    }

    protected String getTableName(PersistMeta meta, Object obj) {
        if (meta.getPostfix() != null) {
            try {
                return meta.getTableName() + '_' + meta.getPostfix().invoke(obj);
            } catch (Exception e) {
                log.error("cannot invoke postfix: {}", meta.getPostfix(), e);
            }
        }
        return meta.getTableName();
    }

    /**
     * 列名判空处理
     *
     * @return boolean
     */
    protected boolean isNull(Field field, Object obj) {
        try {
            return field.get(obj) == null;
        } catch (IllegalAccessException e) {
            return true;
        }
    }

    /**
     * 构建where条件
     */
    private List<String> buildWheres(PersistMeta meta) {
        List<String> wheres = Lists.newArrayList();
        meta.getPkColumns().forEach((columnName, field) -> wheres.add(columnName + "=#{" + field.getName() + "}"));
        return wheres;
    }


    @SuppressWarnings({"unchecked", "rawtypes"})
    public String batchInsert(final Map<String, Object> map) {
        List dataList = (List) map.get(VALUE_KEY);
        if (null == dataList || dataList.isEmpty()) {
            return new SQL().toString();
        }
        Object obj = dataList.get(0);
        PersistMeta meta = EntityUtil.getMeta(obj.getClass());
        StringBuilder fields = new StringBuilder();
        StringBuilder template = new StringBuilder();
        template.append('(');
        int i = 0;

        Map<String, Field> columnMap = Maps.newHashMap();
        dataList.forEach(row -> {
            for (Map.Entry<String, Field> column : meta.getColumns().entrySet()) {
                if (columnMap.containsKey(column.getKey())) {
                    continue;
                }
                if (!isNull(column.getValue(), row)) {
                    columnMap.putIfAbsent(column.getKey(), column.getValue());
                }
            }
        });

        for (Map.Entry<String, Field> kv : columnMap.entrySet()) {
            if (i++ != 0) {
                fields.append(',');
                template.append(',');
            }
            fields.append(kv.getKey());
            Field field = kv.getValue();
            DynamicTypeHandler typeHandler = field.getAnnotation(DynamicTypeHandler.class);
            if (typeHandler == null) {
                if (field.getType().isAssignableFrom(List.class)) {
                    template.append("#'{'value[{0}].").append(field.getName()).append(",typeHandler=").append(ListTypeHandler.class.getName()).append("'}'");
                } else if (field.getType().isAssignableFrom(Set.class)) {
                    template.append("#'{'value[{0}].").append(field.getName()).append(",typeHandler=").append(SetTypeHandler.class.getName()).append("'}'");
                } else {
                    template.append("#'{'value[{0}].").append(field.getName()).append("'}'");
                }
            } else {
                template.append("#'{'value[{0}].").append(field.getName()).append(",typeHandler=").append(typeHandler.value()).append("'}'");
            }
        }
        template.append(')');
        StringBuilder insertSql = new StringBuilder();
        insertSql.append("INSERT INTO ").append(getTableName(meta, obj)).append('(').append(fields).append(')').append(" VALUES ");
        MessageFormat mf = new MessageFormat(template.toString());
        for (int j = 0; j < dataList.size(); j++) {
            if (j != 0) {
                insertSql.append(',');
            }
            insertSql.append(mf.format(new String[]{String.valueOf(j)}));
        }
        return insertSql.toString();
    }

    /**
     * 更新操作
     *
     * @param obj
     * @return String
     */
    public String update(Object obj) {
        return updateWithNull(obj, true);
    }

    private String updateWithNull(Object obj, boolean skipNull) {
        Class<?> clazz = obj.getClass();
        PersistMeta meta = EntityUtil.getMeta(clazz);
        if (CollectionUtils.isEmpty(meta.getPkColumns()) && StringUtils.isNotEmpty(meta.getIdColumnName())) {
            meta.getPkColumns().put(meta.getIdColumnName(), meta.getColumns().get(meta.getIdColumnName()));
        }
        if (CollectionUtils.isEmpty(meta.getPkColumns())) {
            return new SQL().toString();
        }
        StringBuilder setting = new StringBuilder(32);
        int i = 0;
        for (Map.Entry<String, Field> kv : meta.getColumns().entrySet()) {
            Field field = kv.getValue();
            if (meta.getPkColumns().containsKey(kv.getKey())) {
                continue;
            }

            if (skipNull && isNull(field, obj)) {
                continue;
            }

            if (i++ != 0) {
                setting.append(',');
            }
            DynamicTypeHandler typeHandler = field.getAnnotation(DynamicTypeHandler.class);
            if (typeHandler == null) {
                setting.append(FIELD_LEFT).append(kv.getKey()).append(FIELD_RIGHT).append("=#{").append(field.getName()).append('}');
            } else {
                setting
                        .append(FIELD_LEFT)
                        .append(kv.getKey())
                        .append(FIELD_RIGHT)
                        .append("=#{")
                        .append(field.getName())
                        .append(",typeHandler=")
                        .append(typeHandler.value())
                        .append('}');
            }
        }
        return new SQL()
                .UPDATE(getTableName(meta, obj))
                .SET(setting.toString())
                .WHERE(buildWheres(meta).toArray(new String[meta.getPkColumns().size()]))
                .toString();
    }
}
