package com.fxiaoke.open.erpsyncdata.common.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.fxiaoke.open.erpsyncdata.common.util.GenericsUtil;
import com.fxiaoke.open.erpsyncdata.common.util.IdUtil;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import io.protostuff.ProtobufIOUtil;
import io.protostuff.Schema;
import io.protostuff.runtime.RuntimeSchema;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.MDC;

@Slf4j
@AllArgsConstructor
@NoArgsConstructor
public abstract class AbstractMqConsumer<T> {
    protected Class<T> messageClass = GenericsUtil.getSuperClassGenricType(this.getClass());
    private String rocketMQConsumerConfigName;
    private String consumerGroup;
    private String topic;
    private String nameserver;
    private AutoConfMQPushConsumer autoConfRocketMQProcessor;

    public AbstractMqConsumer(String rocketMQConsumerConfigName, String nameserver, String consumerGroup, String topic) {
        this.rocketMQConsumerConfigName = rocketMQConsumerConfigName;
        this.consumerGroup = consumerGroup;
        this.topic = topic;
        this.nameserver = nameserver;
    }

    public void init() {
        MessageListenerOrderly listener = (msgs, context) -> {
            Thread.currentThread().setName("Comsumer-" + this.getClass().getSimpleName() + "-" + IdUtil.generateId().substring(0, 6));
            msgs.forEach(message -> {
                T messageObject = null;
                try {
                    MessageHelper.fillContextFromMessage(TraceContext.get(), message);
                    checkOrSetTraceId();
                    //这里要区分json和probuff格式分别解析
                    if("EnterpriseChangeEvent".equals(message.getTopic())) {
                        messageObject = parsePBObject(message.getBody());
                    }else{
                        messageObject = parseJsonObject(message);
                    }

                    log.debug("comsumer msg msgId={} topic={} tags={} flag={} messageObject={}",
                            message.getMsgId(), message.getTopic(), message.getTags(), message.getFlag(), messageObject);

                    processMessage(messageObject);
                } catch (Throwable e) {
                    Object infoMsg = message;
                    if (messageObject != null) {
                        infoMsg = messageObject;
                    }
                    log.warn("error "+ e.getMessage()+" comsumer msg msgId=" + message.getMsgId() + " topic=" + message.getTopic() + " tags=" + message.getTags() + " flag=" + message.getFlag() + " msg=" + infoMsg + "", e);
                } finally {
                    removeTrace();
                }
            });
            return ConsumeOrderlyStatus.SUCCESS;
        };
        autoConfRocketMQProcessor = new AutoConfMQPushConsumer(rocketMQConsumerConfigName, listener);
        if (!Strings.isNullOrEmpty(consumerGroup)) {
            autoConfRocketMQProcessor.setGroupNameKey(consumerGroup);
        }
        if (!Strings.isNullOrEmpty(nameserver)) {
            autoConfRocketMQProcessor.setNameServerKey(nameserver);
        }
        if (!Strings.isNullOrEmpty(topic)) {
            autoConfRocketMQProcessor.setConsumeTopicKey(topic);
        }

        autoConfRocketMQProcessor.start();
        log.info("[" + this.getClass().getName() + "] [init] [success] [autoConfRocketMQProcessor:{}] ", autoConfRocketMQProcessor);
    }

    /**
     * 处理json格式
     *
     **/
    protected T parseJsonObject(Message message) throws Throwable {
        return JSON.parseObject(message.getBody(), messageClass, Feature.IgnoreNotMatch);
    }

    /**
     * 处理PB格式
     *
     * @param body 消息体
     * @return UnbindWeChat
     */
    protected T  parsePBObject(byte[] body) {
        try {
            T newMsg = messageClass.newInstance();
            Schema schema = RuntimeSchema.getSchema(messageClass);
            ProtobufIOUtil.mergeFrom(body, newMsg, schema);
            return newMsg;
        } catch (Exception e) {
            log.error("pluginMessageListener convert sendPluginEvent bean failed.", e);
        }
        return null;
    }

    protected abstract void processMessage(T message) throws Throwable;

    public void setRocketMQConsumerConfigName(String rocketMQConsumerConfigName) {
        this.rocketMQConsumerConfigName = rocketMQConsumerConfigName;
    }

    public void setConsumerGroup(String consumerGroup) {
        this.consumerGroup = consumerGroup;
    }

    private void checkOrSetTraceId() {
        TraceContext context = TraceContext.get();
        String traceId = context.getTraceId();
        if (StringUtils.isEmpty(traceId)) {
            traceId = "mq_" + System.currentTimeMillis();
            context.setTraceId(traceId);
        }
        MDC.put("traceId", traceId);
    }

    public void removeTrace() {
        TraceContext.remove();
        MDC.remove("traceId");
    }
}
