package com.fxiaoke.open.erpsyncdata.converter.fieldconvert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.*;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.util.FieldTypeUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataCenterManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldDataMappingManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.NullCandidateEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.FieldDescribeData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.ProductCategoryData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.OuterService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncDataMappingService;
import com.fxiaoke.open.erpsyncdata.preprocess.util.SyncObjectFieldUtil;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
@Slf4j
@Component
public class SourceValueFieldConverter implements FieldConverter {
    @Autowired
    private SyncDataMappingService syncDataMappingService;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private ErpFieldDataMappingManager erpFieldDataMappingManager;
    @Autowired
    private DataCenterManager dataCenterManager;

    @Override
    public Object convert(String tenantId, ObjectData sourceData, Integer destEventType, String destTenantId, FieldMappingData fieldMappingData, Integer destTenantType, Integer sourceTenantType, String destObjectApiName) {
        OuterService outerService = outerServiceFactory.get(sourceTenantType);
        Object sourceValue = sourceData.get(fieldMappingData.getSourceApiName());
        String destType = fieldMappingData.getDestType();
        String sourceType = fieldMappingData.getSourceType();
        String sourceObjectApiName = sourceData.getApiName();

        if (sourceType.equals(FieldType.QUOTE)) {
            if (sourceValue == null) {
                String sourceQuoteRealField = fieldMappingData.getSourceQuoteRealField();
                String sourceQuoteFieldTargetObjectField = fieldMappingData.getSourceQuoteFieldTargetObjectField();
                String sourceQuoteFieldTargetObjectApiName = fieldMappingData.getSourceQuoteFieldTargetObjectApiName();
                String quoteFieldValue = sourceData.getString(sourceQuoteRealField);
                if (quoteFieldValue == null) {
                    return null;
                }
                ObjectData quoteTargetObjectData = outerService.getObjectData(sourceData.getTenantId(), sourceTenantType, sourceQuoteFieldTargetObjectApiName, quoteFieldValue, true).getData();
                if (quoteTargetObjectData == null) {
                    return null;
                }
                //获取sourceQuoteFieldTargetObjectField字段描述
                Result2<FieldDescribeData> fieldDescribeResult = outerService.getFieldDescribe(sourceData.getTenantId(), sourceQuoteFieldTargetObjectApiName, sourceQuoteFieldTargetObjectField);
                if (!fieldDescribeResult.isSuccess()) {
                    return null;
                }
                //fieldMappingData 等重新构造后面继续使用
                sourceValue = quoteTargetObjectData.get(sourceQuoteFieldTargetObjectField);
                sourceData.put(fieldMappingData.getSourceApiName(),sourceValue);//把取到的值赋值给sourceData
                sourceType = fieldDescribeResult.getData().getType();
                if (sourceType.equals(FieldType.OBJECT_REFERENCE)) {
                    fieldMappingData.setSourceTargetApiName(fieldDescribeResult.getData().getTargetApiName());
                }
            }
        }
        //先根据配置判断是否为空
        sourceValue = changeToNull(sourceData, fieldMappingData, sourceValue);
        if (sourceValue == null) return null;
        //判断是否需要保留原值返回
        if (BooleanUtil.isTrue(fieldMappingData.getUseSourceValueDirectly())) {
            return sourceValue;
        }

        if (sourceType.equals(FieldType.COUNT) && destType.equals(FieldType.CURRENCY)){
                String sourceObjectDataId = sourceData.getId();
                ObjectData sourceObjectData = outerService.getDetailById(sourceData.getTenantId(),sourceData.getApiName(),sourceObjectDataId).getData();
                return sourceObjectData.get(fieldMappingData.getSourceApiName());
        }
        if (destType.equals(FieldType.SELECT_ONE)) {
            Object destValue = fieldMappingData.getDestOptionBySourceOption(sourceValue);
            if (destValue != null && StringUtils.isNotBlank(String.valueOf(destValue))) {
                return destValue;
            }
            if (sourceTenantType == TenantTypeEnum.ERP.getType() && FieldType.TEXT.equals(sourceType)) {//erp->crm源是文本，直接返回源值
                return sourceValue;
            }
            //erp暂时不走产品分类逻辑，过渡阶段使用
            if (sourceTenantType != TenantTypeEnum.CRM.getType() || destTenantType != TenantTypeEnum.CRM.getType()) {
                return destValue;
            }
            if ((SpuSkuConstant.PRODUCT_OBJ.equals(sourceData.getApiName()) || SpuSkuConstant.SPU_OBJ.equals(sourceData.getApiName()))
                && (SpuSkuConstant.PRODUCT_OBJ.equals(destObjectApiName) || SpuSkuConstant.SPU_OBJ.equals(destObjectApiName))
                && ProductCategoryConstant.CATEGORY.equals(fieldMappingData.getDestApiName())) {
                //拿源企业产品分类
                Result2<ProductCategoryData> sourceProductCategoryDataResult = outerServiceFactory.get(sourceTenantType).getSourceProductCategoryValue(sourceData.getTenantId(), (String) sourceValue);
                if (!sourceProductCategoryDataResult.isSuccess()) {
                    return null;
                }
                ProductCategoryData sourceProductCategoryData = sourceProductCategoryDataResult.getData();
                //获取或创建目标产品分类
                Result2<ProductCategoryData> destProductCategoryDataResult = outerServiceFactory.get(destTenantType).getOrCreateDestProductCategoryValue(destTenantId, sourceProductCategoryData.getName(),
                    sourceProductCategoryData.getCategoryCode(), sourceProductCategoryData.getOrderField());
                if (!destProductCategoryDataResult.isSuccess()) {
                    return null;
                }
                ProductCategoryData destProductCategoryData = destProductCategoryDataResult.getData();
                return destProductCategoryData.getCode();
            }
            return destValue;
        }
        if (destType.equals(FieldType.RECORD_TYPE) || destType.equals(FieldType.BOOL)) {
            if (sourceValue instanceof Boolean) {
                return sourceValue;
            }
            Object destValue = fieldMappingData.getDestOptionBySourceOption(sourceValue);
            return destValue;
        }
        if (destType.equals(FieldType.SELECT_MANY)) {
            List<Object> sourceList = null;
            if (sourceValue instanceof List) {
                sourceList = (List<Object>) sourceValue;
            } else {
                sourceList = new ArrayList<>();
                sourceList.add(sourceValue);
            }
            List<Object> destList = new ArrayList<>(sourceList.size());
            Set<Object> destSet = new LinkedHashSet<>();
            for (Object key : sourceList) {
                Object destKey = fieldMappingData.getDestOptionBySourceOption(key);
                if (destKey != null) {
                    destSet.add(destKey);
                }
            }
            destList.addAll(destSet);
            return destList;
        }
        if (destType.equals(FieldType.EMPLOYEE_MANY)) {
            List<Object> sourceList = null;
            if (sourceValue instanceof List) {
                sourceList = (List<Object>) sourceValue;
            } else {
                sourceList = new ArrayList<>();
                sourceList.add(sourceValue);
            }
            return sourceList;
        }
        if (destType.equals(FieldType.DEPARTMENT_MANY)) {
            log.info("SourceValueFieldConverter.convert,department_many,sourceValue={}",sourceValue);
            List<String> srcDepIdList = new ArrayList<>();
            if (sourceValue instanceof List) {
                srcDepIdList = (List) sourceValue;
            } else {
                String value = sourceValue.toString();
                if(StringUtils.contains(value,",")) {
                    srcDepIdList = Splitter.on(",").splitToList(sourceValue.toString());
                }
                if(StringUtils.contains(value,";")) {
                    srcDepIdList = Splitter.on(";").splitToList(sourceValue.toString());
                }

                if(CollectionUtils.isEmpty(srcDepIdList) && StringUtils.isNotEmpty(value)) {
                    srcDepIdList.add(value);
                }
            }

            List<String> destDepIdList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(srcDepIdList)) {
                String dcId = dataCenterManager.getDataCenterByObjApiName(destTenantId, sourceObjectApiName);
                log.info("SourceValueFieldConverter.convert,department_many,dcId={},sourceObjectApiName={}",dcId,sourceObjectApiName);
                for(String depId : srcDepIdList) {
                    List<ErpFieldDataMappingEntity> entityList = erpFieldDataMappingManager.listNoSearch(tenantId,
                            dcId,
                            ErpFieldTypeEnum.department,
                            null,
                            depId);
                    log.info("SourceValueFieldConverter.convert,department_many,entityList={}",entityList);
                    if(CollectionUtils.isNotEmpty(entityList)) {
                        destDepIdList.add(entityList.get(0).getFsDataId());
                    } else {
                        destDepIdList.add(depId);
                    }
                }
            }
            log.info("SourceValueFieldConverter.convert,department_many,destDepIdList={}",destDepIdList);
            return destDepIdList;
        }
        if (sourceType.equals(FieldType.DATE) && destType.equals(FieldType.DATE) ){
            if (sourceValue instanceof BigDecimal){
                return ((BigDecimal) sourceValue).longValue();
            }
        }
        if (sourceType.equals(FieldType.DEPARTMENT) && FieldTypeUtil.isStringType(destType)) {
            List<String> deptIds = (List<String>) sourceValue;
            if (deptIds.isEmpty()) {
                return null;
            }
            return outerService.getDeptName(sourceData.getTenantId(), sourceTenantType, deptIds.get(0)).getData();
        }
        if (sourceType.equals(FieldType.OBJECT_REFERENCE) && FieldTypeUtil.isStringType(destType)) {
            return outerService.getReferName(sourceData.getTenantId(), sourceTenantType, fieldMappingData.getSourceTargetApiName(), String.valueOf(sourceValue)).getData();
        }
        if (sourceType.equals(FieldType.EMPLOYEE) && FieldTypeUtil.isStringType(destType)) {
            List<String> employeeIds = (List<String>) sourceValue;
            if (employeeIds.isEmpty()) {
                return null;
            }
            String fieldName = fieldMappingData.getValue();
            Map<String, String> names = outerService.batchGetEmployeeFieldValue(sourceData.getTenantId(), null, employeeIds, fieldName).getData();
            return names.get(employeeIds.get(0));
        }
        if (sourceType.equals(FieldType.RECORD_TYPE) && destType.equals(FieldType.SELECT_ONE)) {
            Object destValue = fieldMappingData.getDestOptionBySourceOption(sourceValue);
            return destValue;
        }
        if (FieldTypeUtil.isStringType(destType)) {
            return String.valueOf(sourceValue);
        }
        if (sourceType.equals(FieldType.MASTER_DETAIL) && destType.equals(FieldType.MASTER_DETAIL)) {
            return convertObjectReference(tenantId, sourceData.getTenantId(), sourceValue, destTenantId, fieldMappingData);
        }
        if (SyncObjectFieldUtil.isObjectReferenceFieldType(fieldMappingData.getSourceType())
                && SyncObjectFieldUtil.isObjectReferenceFieldType(fieldMappingData.getDestType())) {
            return convertObjectReference(tenantId, sourceData.getTenantId(), sourceValue, destTenantId, fieldMappingData);
        }
        if(StringUtils.equalsIgnoreCase(sourceType,FieldType.OBJECT_REFERENCE_MANY)) {
            return convertObjectReferenceMany(tenantId, sourceData.getTenantId(), sourceValue, destTenantId, fieldMappingData);
        }
        if (SyncObjectFieldUtil.isFileFieldType(fieldMappingData.getSourceType()) && SyncObjectFieldUtil.isFileFieldType(fieldMappingData.getDestType())) {
            // 文件转换不走这里，走的是AttachmentFieldConvert
            log.warn("KNY says the code doesn't reach this branch.");
            return convertFile(sourceData.getTenantId(), fieldMappingData.getSourceType(), (List<Map<String, String>>) sourceValue, destTenantId, sourceTenantType, destTenantType);
        }
        return sourceValue;
    }

    /**
     * 判断是否为空
     */
    private static Object changeToNull(ObjectData sourceData, FieldMappingData fieldMappingData, Object sourceValue) {
        if (CollUtil.isNotEmpty(fieldMappingData.getNullCandidateList())) {
            Set<NullCandidateEnum> nullCandidateEnums = NullCandidateEnum.fromStrList(fieldMappingData.getNullCandidateList());
            if (nullCandidateEnums.contains(NullCandidateEnum.IS_BLANK_IF_STR) && StrUtil.isBlankIfStr(sourceValue)) {
                return null;
            }
            if (nullCandidateEnums.contains(NullCandidateEnum.IS_EMPTY_IF_STR) && StrUtil.isEmptyIfStr(sourceValue)) {
                return null;
            }
            if (nullCandidateEnums.contains(NullCandidateEnum.IS_NULL) && sourceValue == null) {
                return null;
            }
            if (nullCandidateEnums.contains(NullCandidateEnum.NOT_EXIST) && sourceData.containsKey(fieldMappingData.getSourceApiName())) {
                return null;
            }
        } else {
            //默认情况是，空值或者空字符串为null
            if (sourceValue == null || StringUtils.isBlank(sourceValue.toString())) {
                return null;
            }
        }
        return sourceValue;
    }

    @Override
    public Integer getFieldMappingType() {
        return FieldMappingTypeEnum.SOURCE_VALUE.getType();
    }

    private String convertObjectReference(String tenantId, String sourceTenantId, @NotNull Object sourceValue, String destTenantId, FieldMappingData fieldMapping) {
        SyncDataMappingData dataMappingData = syncDataMappingService
                .getSyncDataMapping(tenantId, sourceTenantId, fieldMapping.getSourceTargetApiName(), String.valueOf(sourceValue), destTenantId, fieldMapping.getDestTargetApiName()).getData();
        if (dataMappingData == null && BooleanUtils.isTrue(fieldMapping.getNotCheckMappingField())) {
            return null;
        }
        return dataMappingData.getDestDataId();
    }

    private Object convertObjectReferenceMany(String tenantId,String sourceTenantId, Object sourceValue, String destTenantId, FieldMappingData fieldMapping) {
        List<String> sourceValueList = null;
        if(sourceValue instanceof List) {
            sourceValueList = (List<String>) sourceValue;
        } else {
            sourceValueList = new ArrayList<>();
            sourceValueList.add(String.valueOf(sourceValue));
        }
        List<String> destValueList = new ArrayList<>();
        for(String value : sourceValueList) {
            if(StringUtils.isEmpty(value)) continue;

            String sourceTargetApiName = fieldMapping.getSourceTargetApiName();
            String destTargetApiName = fieldMapping.getDestTargetApiName();

            log.info("SourceValueFieldConverter.convertObjectReference,fieldMapping={}",fieldMapping);
            SyncDataMappingData dataMappingData = syncDataMappingService
                    .getSyncDataMapping(tenantId,
                            sourceTenantId,
                            sourceTargetApiName,
                            value,
                            destTenantId,
                            destTargetApiName).getData();
            if(dataMappingData==null) continue;
            destValueList.add(dataMappingData.getDestDataId());
        }
        return destValueList;
    }

    private List<Map<String, String>> convertFile(String sourceTenantId, String type, List<Map<String, String>> values, String destTenantId, Integer sourceTenantType, Integer destTenantType) {
        List<Map<String, String>> files = outerServiceFactory.get(sourceTenantType).convertFiles(sourceTenantId, sourceTenantType, type, values, destTenantId, destTenantType).getData();
        return files;
    }
}
