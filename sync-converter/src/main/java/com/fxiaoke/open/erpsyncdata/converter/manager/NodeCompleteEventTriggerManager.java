package com.fxiaoke.open.erpsyncdata.converter.manager;

import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.crmrestapi.arg.v3.GetByIdArg;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataGetByIdV3Result;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.open.erpsyncdata.common.annotation.CompareSyncField;
import com.fxiaoke.open.erpsyncdata.common.constant.*;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.data.bizlog.SyncDataErrLog;
import com.fxiaoke.open.erpsyncdata.common.util.LogUtil;
import com.fxiaoke.open.erpsyncdata.common.util.SandboxUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.data.GetOrCreateByTwoWayData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.AbsMainNodeProcessor;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.NodeHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ExecuteCustomFunctionArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ExecuteCustomFunctionArg.ExecuteCustomFunctionParameterData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogStatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.FunctionServiceExecuteReturnData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.EventTriggerService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.OverrideOuterService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.fxiaoke.otherrestapi.syncservice.constant.ResultCodeEnum;
import com.fxiaoke.ps.ProtostuffUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * 待数据存储MQ
 */
@Slf4j
@Component
public class NodeCompleteEventTriggerManager extends AbsMainNodeProcessor {
    @Autowired
    private SyncDataMappingManager syncDataMappingManager;
    @Autowired
    private SyncDataFixDao syncDataDao;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private SyncDataManager syncDataManager;
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
    @Resource(name = "overrideOuterService")
    private OverrideOuterService overrideOuterService;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;

    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private CrmMetaManager crmMetaManager;
    @Autowired
    private EventTriggerService eventTriggerService;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    @Lazy
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private EIEAConverter eieaConverter;

    public NodeCompleteEventTriggerManager() {
        super(DataNodeNameEnum.DataBeforeFunc);
    }

    @Override
    protected void postReport(SyncDataContextEvent ctx) {
        ErpTempData erpTempData = ErpTempData.builder().dataId(ctx.getSourceData().getId()).dataNumber(ctx.getSourceData().getName()).build();
        String tenantId = ctx.getTenantId();
        String streamId = ctx.getStreamId();
        setDataVersion(tenantId,ctx,erpTempData);
        String mongoId = ctx.getSourceData().getString("mongo_id");//从mongo获取的erp主数据才有这个字段
        if (mongoId == null && ctx.getUpdateData() != null) {
            mongoId = ctx.getUpdateData().getString("mongo_id");
        }
        if (ctx.getStop()) {
            //同步中断
            erpTempData.setRemark(ctx.getMsg());
            syncLogManager.saveErpTempLog(tenantId, SyncLogTypeEnum.DATA_SYNC_FILTER, SyncLogStatusEnum.SYNC_FAIL.getStatus(), null, erpTempData);
            overrideOuterService.updateErpTempDataByIds(tenantId, streamId, mongoId, 10070, ctx.getMsg());
            //同步中断的时候，仅记录主
            if (ctx.isMainObj()) {
                NodeHelper.addStatusRecord(NodeDataStatus.BLOCKED, ctx.getMainIdIfExist(), ctx.getAllObjCount());
            }
        } else {
            //这里才记录触发的日志
            String triggerMsg = I18NStringEnum.s896.getText();
            if (ctx.getDataReceiveType() != null) {
                if(StringUtils.isNotBlank(ctx.getSourceRemark())){
                    triggerMsg = ctx.getSourceRemark() + "," + triggerMsg;
                }else{
                    DataReceiveTypeEnum dataReceiveTypeEnum = DataReceiveTypeEnum.getByType(ctx.getDataReceiveType());
                    if (dataReceiveTypeEnum != null&&!dataReceiveTypeEnum.equals(DataReceiveTypeEnum.OTHER)) {
                        triggerMsg = dataReceiveTypeEnum.getI18NStringEnum().getText() + "," + triggerMsg;
                    }
                }
            }
            //增加触发字段
            if (StrUtil.isNotEmpty(ctx.getMatchField())) {
                triggerMsg = triggerMsg + "," + I18NStringEnum.s4018.getText() + "(" + ctx.getMatchField() + "...)";
            }
            erpTempData.setRemark(triggerMsg);
            //正常结束
            overrideOuterService.updateErpTempDataByIds(tenantId, streamId, mongoId, 10060, i18NStringManager.getByEi(I18NStringEnum.s896, tenantId));
            syncLogManager.saveErpTempLog(tenantId, SyncLogTypeEnum.DATA_SYNC_FILTER, SyncLogStatusEnum.SYNC_SUCCESS.getStatus(), null, erpTempData);
            if (ctx.isMainObj()) {
                //能通过的明细数量
                int detailCount = Opt.ofNullable(ctx.getDetailObjectDatasMap())
                        .map(v -> v.values())
                        .map(v -> v.stream().mapToInt(v1 -> v1.size()).sum())
                        .orElse(0);
                NodeHelper.addStatusRecord(NodeDataStatus.FILTERED, ctx.getMainIdIfExist(), detailCount + 1);
                //如果主通过了，但是通过的明细数量比原始的少，那上报一下没通过的数量。仅ERP来源的有效，CRM的来源数量为0
                int blockedCount = ctx.getAllObjCount() - detailCount - 1;
                if (blockedCount > 0) {
                    //ERP来源的，仅主对象数据记录，且记录原始的数据量
                    NodeHelper.addStatusRecord(NodeDataStatus.BLOCKED, ctx.getMainIdIfExist(), blockedCount);
                }
            }
        }
    }

    @CompareSyncField(syncType = SyncCompareConstant.SYNC_PRE_FUNCTION)
    public SyncDataContextEvent processMessage(final SyncDataContextEvent message) {
        String tenantId = message.getTenantId();
        String syncPloyDetailSnapshotId = message.getSyncPloyDetailSnapshotId();
        //明细的新增或者更新会触发主对象同步，然后stop
        if (message.getSourceTenantType() == TenantTypeEnum.CRM.getType()
                && (message.getSourceEventType().equals(EventTypeEnum.UPDATE.getType()) || message.getSourceEventType().equals(EventTypeEnum.ADD.getType()) || message.getSourceEventType().equals(EventTypeEnum.INVALID.getType()) || message.getSourceEventType().equals(EventTypeEnum.RECOVER.getType()))
                && !message.isMainObj()) {
            crmDetailTriggerMasterObjectUpdate(message);
            log.info("complete sync detail trigger master data:{}", message);
            if (message.getSourceEventType().equals(EventTypeEnum.UPDATE.getType()) || message.getSourceEventType().equals(EventTypeEnum.ADD.getType())) {
                //只有新增/更新才中止继续
                return message.stop("complete sync detail trigger master data");
            }
        }
        Integer sourceEventType = message.getSourceEventType();
        //同步前执行自定义函数
        executeCustomFunction(message);
        if (message.getStop()) {
            //中断同步
            return message;
        }
        if (!message.isSuccess()) {
            String syncDataId = new ObjectId().toString();
            SyncDataMappingsEntity mappingsEntity = syncDataMappingManager
                    .getOrCreateSyncDataMappingByTwoWay(tenantId, syncDataId, SyncDataStatusEnum.TRIGGER_FAILED.getStatus(), message.getDestObjectApiName(), message.getDestTenantId(), message.getSourceData(), true,message.getSourceMasterId())
                    .getSourcedData();
            syncDataManager
                    .createSyncData2(tenantId,message.getSourceContextUserId(), syncDataId, mappingsEntity.getId(), message.getSourceEventType(), message.getSourceTenantType(), message.getSourceData(), SyncDataStatusEnum.TRIGGER_FAILED.getStatus(),
                            syncPloyDetailSnapshotId, null, null, message.getDestObjectApiName(), message.getDestTenantId(), message.getDestTenantType(), message.getMasterMappingsData(), message.getErrMsg(),String.valueOf(message.getErrCode()), message.getDataReceiveType(),message.getDataVersion());
            return message.stop(message.getErrMsg());
        }
        if (sourceEventType.equals(EventTypeEnum.WAITTING.getType())) {
            return processWattingMessage(message);
        } else if (sourceEventType.equals(EventTypeEnum.INVALID.getType()) || sourceEventType.equals(EventTypeEnum.DELETE_DIRECT.getType())) {
            return processInvalidMessage(message);
            //补全逻辑挪到前面后，依赖的处理和正常的一样了。
//        } else if (sourceEventType.equals(EventTypeEnum.DEPEND.getType())) {
//            return processDependMessage(message);
        } else if (sourceEventType.equals(EventTypeEnum.RECOVER.getType())) {
            return processRecoverMessage(message);
        } else {
            return processOtherMessage(message);
        }
    }
    @CompareSyncField(syncType = SyncCompareConstant.SYNC_PROCESS_RECOVER)
    private SyncDataContextEvent processRecoverMessage(SyncDataContextEvent syncDataContextEvent) {
        String tenantId = syncDataContextEvent.getTenantId();
        String syncDataId = new ObjectId().toString();
        String syncPloyDetailSnapshotId = syncDataContextEvent.getSyncPloyDetailSnapshotId();
        ObjectData sourceData = syncDataContextEvent.getSourceData();
        Integer sourceEventType = syncDataContextEvent.getSourceEventType();
        String destObjectApiName = syncDataContextEvent.getDestObjectApiName();
        String destTenantId = syncDataContextEvent.getDestTenantId();
        //校验过，肯定有映射
        GetOrCreateByTwoWayData getOrCreateByTwoWayData = syncDataMappingManager
                .getOrCreateSyncDataMappingByTwoWay(tenantId, syncDataId, SyncDataStatusEnum.BE_PROCESS.getStatus(), destObjectApiName, destTenantId, sourceData, false,syncDataContextEvent.getSourceMasterId());
        SyncDataMappingsEntity dataMappingData = getOrCreateByTwoWayData.getSourcedData();
        String destDataId = null;
        if (dataMappingData == null) {
            dataMappingData = getOrCreateByTwoWayData.getDestData();
            //取另一个方向的源数据id就是本次事件的目标数据id
            destDataId = dataMappingData.getSourceDataId();
        } else {
            destDataId = dataMappingData.getDestDataId();
        }
        syncDataManager
                .createSyncData2(tenantId,syncDataContextEvent.getSourceContextUserId(), syncDataId, dataMappingData.getId(), sourceEventType, syncDataContextEvent.getSourceTenantType(), sourceData, SyncDataStatusEnum.BE_PROCESS.getStatus(), syncPloyDetailSnapshotId,
                        destDataId, EventTypeEnum.RECOVER.getType(), destObjectApiName, destTenantId, syncDataContextEvent.getDestTenantType(), syncDataContextEvent.getMasterMappingsData(), null, null, syncDataContextEvent.getDataReceiveType(),syncDataContextEvent.getDataVersion());

        syncDataContextEvent.setDestEventType(EventTypeEnum.RECOVER.getType());
        syncDataContextEvent.setDestObjectApiName(syncDataContextEvent.getDestObjectApiName());
        syncDataContextEvent.setDestTenantId(syncDataContextEvent.getDestTenantId());
        syncDataContextEvent.setSourceTenantId(syncDataContextEvent.getSourceData().getTenantId());
        syncDataContextEvent.setSyncDataId(syncDataId);
        syncDataContextEvent.setDestDataId(destDataId);
        syncDataContextEvent.setDestTenantType(syncDataContextEvent.getDestTenantType());
        syncDataContextEvent.setMasterMappingsData(syncDataContextEvent.getMasterMappingsData());
        syncDataManager.fillSyncDataMap(syncDataContextEvent);
        return syncDataContextEvent.next();
    }
    @CompareSyncField(syncType = SyncCompareConstant.SYNC_PROCESS_WAITING)
    protected SyncDataContextEvent processWattingMessage(SyncDataContextEvent syncDataContextEvent) {
        String tenantId = syncDataContextEvent.getTenantId();
        String syncDataId = syncDataContextEvent.getSyncDataId();
        SyncDataEntity syncData = syncDataDao.setTenantId(tenantId).getById(tenantId, syncDataId);
        String syncPloyDetailSnapshotId = syncData.getSyncPloyDetailSnapshotId();
        ObjectData sourceData = syncData.getSourceData();
        Integer sourceEventType = syncData.getSourceEventType();
        String destTenantId = syncData.getDestTenantId();
        String destObjectApiName = syncData.getDestObjectApiName();
        GetOrCreateByTwoWayData getOrCreateByTwoWayData = syncDataMappingManager
                .getOrCreateSyncDataMappingByTwoWay(tenantId, syncDataId, SyncDataStatusEnum.WAITTING.getStatus(), destObjectApiName, destTenantId, sourceData, false,syncDataContextEvent.getSourceMasterId());
        SyncDataMappingsEntity dataMappingData = getOrCreateByTwoWayData.getSourcedData();
        if (!dataMappingData.getLastSyncDataId().equals(syncDataId)
                && sourceEventType == EventTypeEnum.ADD.getType() && dataMappingData.getIsCreated()) {
//            syncDataManager.updateStatus(tenantId, syncDataId, SyncDataStatusEnum.IGNORE.getStatus(), "已有其他任务执行");
            log.info("{}syncData is not lastSyncData,syncDataId={},dataMappingData={}", LogUtil.BREAK_KEY, syncDataId, dataMappingData);
            return syncDataContextEvent.stop("syncData is not lastSyncData");
        }

        /**
         * 曾现场景：维护日志多个相同超时异常：同时多个依赖处理同一个数据，创建多个事件，处理完第一个之后，第二个待处理时发现上一个已经处理过，因此就不再处理；
         *  但是依赖处理每分钟会触发处理一次，因此会导致在超时时间30分钟内会发送30次到完成触发MQ,但是一直未到此return，导致到30分钟后超时会抛超时异常
         */
        if (SyncDataStatusEnum.isDoingOrWaitting(dataMappingData.getLastSyncStatus()) && !dataMappingData.getLastSyncDataId().equals(syncDataId)) {
            log.info("{}syncData is doing,syncDataId={},dataMappingData={}", LogUtil.BREAK_KEY, syncDataId, dataMappingData);
            return syncDataContextEvent.stop("syncData is doing");
        }
        Long version = sourceData.getVersion();
        if (version != null && dataMappingData.getLastSourceDataVserion() != null && version < dataMappingData.getLastSourceDataVserion()) {
//            syncDataManager.updateStatus(tenantId, syncDataId, SyncDataStatusEnum.IGNORE.getStatus(), "已同步更新的数据");
            log.info("{}syncData is old,syncDataId={},dataMappingData={}", LogUtil.BREAK_KEY, syncDataId, dataMappingData);
            return syncDataContextEvent.stop("syncData is old");
        }
        int destEventType = EventTypeEnum.UPDATE.getType();
        if (!dataMappingData.getIsCreated()) {
            destEventType = EventTypeEnum.ADD.getType();
            //判断从对象是否需要执行
            if (!checkDetailDataToProcessed(sourceData, syncPloyDetailSnapshotId)) {
                log.info("{}detail not have masterId,sourceData={}", LogUtil.BREAK_KEY, sourceData);
                return syncDataContextEvent.stop("detail not have masterId");
            }
        }
        if (sourceEventType == EventTypeEnum.ADD.getType() && destEventType == EventTypeEnum.UPDATE.getType()) {
            syncDataManager.updateStatus(tenantId, syncDataId, SyncDataStatusEnum.WRITE_SUCCESS.getStatus(), i18NStringManager.getByEi(I18NStringEnum.s916,tenantId), I18NStringEnum.s916.name());
            log.info("{}syncData is had add,syncDataId={},dataMappingData={}", LogUtil.BREAK_KEY, syncDataId, dataMappingData);
            return syncDataContextEvent.stop("syncData is had add");
        }
        int updateSyncDataSuccess = syncDataManager
                .updateDestEventTypeAndDestDataIdAndStatus(tenantId, dataMappingData.getId(), syncDataId, destEventType, dataMappingData.getDestDataId(), SyncDataStatusEnum.WAITTING.getStatus(),
                        SyncDataStatusEnum.BE_PROCESS.getStatus(), version, syncPloyDetailSnapshotId);
        if (updateSyncDataSuccess == 0) {
            log.info("{}syncData not watting status,syncDataId={},dataMappingData={}", LogUtil.BREAK_KEY, syncDataId, dataMappingData);
            return syncDataContextEvent.stop("syncData not watting status");
        }
        syncData.setDestEventType(destEventType);
        return syncDataContextEvent.next();
    }
    @CompareSyncField(syncType = SyncCompareConstant.SYNC_PROCESS_INVALID)
    protected SyncDataContextEvent processInvalidMessage(SyncDataContextEvent syncDataContextEvent) {
        String tenantId = syncDataContextEvent.getTenantId();
        String syncDataId = new ObjectId().toString();
        String syncPloyDetailSnapshotId = syncDataContextEvent.getSyncPloyDetailSnapshotId();
        ObjectData sourceData = syncDataContextEvent.getSourceData();
        Integer sourceEventType = syncDataContextEvent.getSourceEventType();
        String destObjectApiName = syncDataContextEvent.getDestObjectApiName();
        String destTenantId = syncDataContextEvent.getDestTenantId();
        GetOrCreateByTwoWayData getOrCreateByTwoWayData = syncDataMappingManager
                .getOrCreateSyncDataMappingByTwoWay(tenantId, syncDataId, SyncDataStatusEnum.BE_PROCESS.getStatus(), destObjectApiName, destTenantId, sourceData, false,syncDataContextEvent.getSourceMasterId());
        SyncDataMappingsEntity dataMappingData = getOrCreateByTwoWayData.getSourcedData();
        String destDataId = null;
        // 双向映射，作废如果没有开启新增事件规则，则有目标到源企业的映射是没法创建的
        if (dataMappingData == null) {
            dataMappingData = getOrCreateByTwoWayData.getDestData();
            //取另一个方向的源数据id就是本次事件的目标数据id
            destDataId = dataMappingData.getSourceDataId();
        } else {
            destDataId = dataMappingData.getDestDataId();
        }
        if (!dataMappingData.getIsCreated()) {
            //没有DestDataId说明正在同步，转成待等待状态
            syncDataManager
                    .createSyncData2(tenantId,syncDataContextEvent.getSourceContextUserId(), dataMappingData.getId(), syncDataId, sourceEventType, syncDataContextEvent.getSourceTenantType(), sourceData, SyncDataStatusEnum.WAITTING.getStatus(), syncPloyDetailSnapshotId,
                            destDataId, null, destObjectApiName, destTenantId, syncDataContextEvent.getDestTenantType(), syncDataContextEvent.getMasterMappingsData(), i18NStringManager.getByEi(I18NStringEnum.s3772, tenantId), I18NStringEnum.s667.name(),syncDataContextEvent.getDataReceiveType(),syncDataContextEvent.getDataVersion());
            return syncDataContextEvent.stop("228");
        }
        if (syncDataContextEvent.getSourceEventType().equals(EventTypeEnum.INVALID.getType())) {
            syncDataManager
                    .createSyncData2(tenantId,syncDataContextEvent.getSourceContextUserId(), syncDataId, dataMappingData.getId(), sourceEventType, syncDataContextEvent.getSourceTenantType(), sourceData, SyncDataStatusEnum.BE_PROCESS.getStatus(), syncPloyDetailSnapshotId,
                            destDataId, EventTypeEnum.INVALID.getType(), destObjectApiName, destTenantId, syncDataContextEvent.getDestTenantType(), syncDataContextEvent.getMasterMappingsData(), null, null, syncDataContextEvent.getDataReceiveType(),syncDataContextEvent.getDataVersion());

            syncDataContextEvent.setDestEventType(EventTypeEnum.INVALID.getType());
            syncDataContextEvent.setSyncDataId(syncDataId);
            syncDataContextEvent.setDestDataId(destDataId);
            syncDataManager.fillSyncDataMap(syncDataContextEvent);
            return syncDataContextEvent.next();
//            dataCenterMqProducerFactory.get(message.getDestTenantType()).sendDoWrite(doWriteMqData);
        } else if (syncDataContextEvent.getSourceEventType().equals(EventTypeEnum.DELETE_DIRECT.getType())) {
            syncDataManager
                    .createSyncData2(tenantId,syncDataContextEvent.getSourceContextUserId(), syncDataId, dataMappingData.getId(), sourceEventType, syncDataContextEvent.getSourceTenantType(), sourceData, SyncDataStatusEnum.BE_PROCESS.getStatus(), syncPloyDetailSnapshotId,
                            destDataId, EventTypeEnum.DELETE_DIRECT.getType(), destObjectApiName, destTenantId, syncDataContextEvent.getDestTenantType(), syncDataContextEvent.getMasterMappingsData(), null, null, syncDataContextEvent.getDataReceiveType(),syncDataContextEvent.getDataVersion());

            syncDataContextEvent.setDestEventType(EventTypeEnum.DELETE_DIRECT.getType());
            syncDataContextEvent.setSyncDataId(syncDataId);
            syncDataContextEvent.setDestDataId(destDataId);
            //doWriteMqData.setDestMasterDataId(message.getDestMasterDataId());
            syncDataManager.fillSyncDataMap(syncDataContextEvent);
            return syncDataContextEvent.next();
//            dataCenterMqProducerFactory.get(message.getDestTenantType()).sendDoWrite(doWriteMqData);
        }
        return syncDataContextEvent.stop(I18NStringEnum.s496.getText());
    }


    /**
     * 新增更新正常的处理逻辑
     */
    @CompareSyncField(syncType = SyncCompareConstant.SYNC_PROCESS_OTHER)
    protected SyncDataContextEvent processOtherMessage(SyncDataContextEvent syncDataContextEvent) {
        String tenantId = syncDataContextEvent.getTenantId();
        String syncDataId = new ObjectId().toString();
        String syncPloyDetailSnapshotId = syncDataContextEvent.getSyncPloyDetailSnapshotId();
        ObjectData sourceData = syncDataContextEvent.getSourceData();
        if (Objects.isNull(sourceData)) {
            log.info("CompleteEventTriggerManager.processOtherMessage sourceData=null");
            return syncDataContextEvent.stop("CompleteEventTriggerManager.processOtherMessage sourceData=null");
        }

        ObjectData updateData = syncDataContextEvent.getUpdateData();
        String sourceTenantId = sourceData.getTenantId();
        Integer sourceEventType = syncDataContextEvent.getSourceEventType();
        String sourceObjectApiName = sourceData.getApiName();
        String sourceDataId = sourceData.getId();
        String destObjectApiName = syncDataContextEvent.getDestObjectApiName();
        String destTenantId = syncDataContextEvent.getDestTenantId();

        Map<String, List<String>> sourceDetailSyncDataIds = null;

        GetOrCreateByTwoWayData createResult = syncDataMappingManager
                .getOrCreateSyncDataMappingByTwoWay(tenantId, syncDataId, SyncDataStatusEnum.BE_PROCESS.getStatus(), destObjectApiName, destTenantId, sourceData, true,syncDataContextEvent.getSourceMasterId());
        SyncDataMappingsEntity dataMappingData = createResult.getSourcedData();
        log.info("CompleteEventTriggerManager.processOtherMessage,dataMappingData={}",dataMappingData);
        String destDataId = dataMappingData.getDestDataId();
        //新增、修改事件处理
        Integer destEventType;
        //如果目标事件为新增事件，判断所有明细是否存在已成功创建的，如果有更新主数据的备注为异常
        if (!dataMappingData.getIsCreated() && !CollectionUtils.isEmpty(syncDataContextEvent.getDetailObjectDatasMap())) {
            StringBuilder remark = new StringBuilder();
            final String errorCode = invalidDetailIds(syncDataContextEvent, tenantId, syncPloyDetailSnapshotId, sourceObjectApiName, remark);
            if (StringUtils.isNotBlank(errorCode)) {
                //再次判断中间表是否已经创建了，如果还不是，才创建一个失败的syncData,防止并发造成存在失败记录
                boolean isExist = syncDataMappingManager.existByTwoWay(tenantId, sourceObjectApiName, sourceDataId, destObjectApiName);
                if (!isExist) {
                    syncDataManager.createSyncData(tenantId,syncDataContextEvent.getSourceContextUserId(), syncDataId, dataMappingData.getId(), sourceEventType, syncDataContextEvent.getSourceTenantType(), sourceData, new ObjectData(), SyncDataStatusEnum.PROCESS_FAILED.getStatus(), syncPloyDetailSnapshotId,
                            destDataId, EventTypeEnum.ADD.getType(), destObjectApiName, destTenantId, syncDataContextEvent.getDestTenantType(), sourceDetailSyncDataIds, remark.toString(), errorCode, syncDataContextEvent.getMasterMappingsData(), syncDataContextEvent.getDataReceiveType(),syncDataContextEvent.getDataVersion());
                }
                return syncDataContextEvent.stop(remark.toString());
            }
        }
        //映射目标数据未创建成功，但是已经有映射记录
        if (!dataMappingData.getIsCreated()) {
            destEventType = EventTypeEnum.ADD.getType();
            //syncDataId与存储同步记录的最后一次同步数据id不等，说明数据已经被处理过
            /**
             * 已经有!createResult.isCreate() 这个判断是否有必要使用 !syncDataId.equals(dataMappingData.getLastSyncDataId())这个判断
             * 映射数据非当前好创建，此前就存在
             */
            if (!createResult.isCreate() && SyncDataStatusEnum.isDoingOrWaitting(dataMappingData.getLastSyncStatus()) && !syncDataId.equals(dataMappingData.getLastSyncDataId())) {
                /**
                 * 数据映射已经存在且旧数据映射正处在处理中/等待中的状态时，若是依赖处理或者初始化处理则没必要再去新增，需要等待旧的映射处理完成(成功或者失败均有可能)
                 */
                if (sourceEventType == EventTypeEnum.ADD.getType() || sourceEventType == EventTypeEnum.INIT.getType()) {
                    log.info("Add-SyncData already exist,sourceEventType={}，mapping={}", sourceEventType, dataMappingData);
                    return syncDataContextEvent.stop("Add-SyncData already exist");
                }
                if (syncDataContextEvent.getIsMatchUpdateData() != null && !syncDataContextEvent.getIsMatchUpdateData()) {
                    log.info("Add-SyncData update is not match,updateData={},sourceDataId={}", updateData, sourceDataId);
                    return syncDataContextEvent.stop("Add-SyncData update is not match");
                }
                /**
                 * 如果发现有对应的映射数据在处理，需要等待其处理完成在处理新的事件
                 */
                //需要补齐填充从对象
//                if (isfillUpDetailObject) {
//                    sourceDetailSyncDataIds = createDetailSyncDataAndMapping(tenantId, sourceObjectApiName, syncPloyDetailSnapshotId, message.getDetailObjectDatasMap(), sourceEventType, destEventType,
//                            message.getSourceTenantType(), message.getDestTenantType());
//                }
//                syncDataManager
//                        .createSyncData(tenantId, syncDataId, dataMappingData.getId(), message.getSourceEventType(), message.getSourceTenantType(), sourceData, null, SyncDataStatusEnum.WAITTING.getStatus(),
//                                syncPloyDetailSnapshotId, dataMappingData.getDestDataId(), destEventType, destObjectApiName, destTenantId, message.getDestTenantType(), sourceDetailSyncDataIds, null, message.getMasterMappingsData());
//                //发送一次更新的事件
                dispatcherSendDelay(syncDataContextEvent);
                log.info("Add-SyncData is doing,sourceEventType={},updateData={},mapping={}", sourceEventType, updateData, dataMappingData);
                return syncDataContextEvent.stop("Add-SyncData is doing");
            }
        } else {//目标映射数据创建成功
            Long version = syncDataContextEvent.getSourceData().getVersion();
            destEventType = EventTypeEnum.UPDATE.getType();
            if (sourceEventType == EventTypeEnum.INIT.getType()) {
                log.info("Add-SyncData already exist for init,sourceEventType={}，mapping={}", sourceEventType, dataMappingData);
                return syncDataContextEvent.stop("Add-SyncData already exist for init");
            }
            if (version != null && dataMappingData.getLastSourceDataVserion() != null && version < dataMappingData.getLastSourceDataVserion()) {
                log.info("{}source version is old,version={},mapping={}", LogUtil.BREAK_KEY, version, dataMappingData);
                return syncDataContextEvent.stop("source version is old");
            }
            if (sourceEventType == EventTypeEnum.ADD.getType()) {
                if(syncDataId.equals(dataMappingData.getLastSyncDataId())){//当映射是当前同步创建的才更新这个状态和备注
                    syncDataMappingManager.updateLastSyncStatusById(tenantId, dataMappingData.getId(), SyncDataStatusEnum.WRITE_SUCCESS.getStatus(), i18NStringManager.getByEi(I18NStringEnum.s917,tenantId));
                }
                log.info("{}syncData is had add,sourceData={},dataMappingData={}", LogUtil.BREAK_KEY, sourceData, dataMappingData);
                return syncDataContextEvent.stop("syncData is had add");
            }
            if (sourceEventType == EventTypeEnum.UPDATE.getType() && updateData != null) {
                updateData.putTenantId(sourceTenantId);
                updateData.putId(sourceDataId);
                updateData.putApiName(sourceObjectApiName);
                updateData.putLastModifiedBy(sourceData.getLastModifiedBy());
                //直接使用全量数据，
                sourceData = BeanUtil.deepCopy(syncDataContextEvent.getSourceData(),ObjectData.class);
            }
            if (SyncDataStatusEnum.isDoingOrWaitting(dataMappingData.getLastSyncStatus()) && !syncDataId.equals(dataMappingData.getLastSyncDataId())) {
                //TODO 后面扫描任务不依赖快照，所以这个创建快照的意义也不大
                //需要补齐填充从对象
//                if (isfillUpDetailObject) {
//                    sourceDetailSyncDataIds = createDetailSyncDataAndMapping(tenantId, sourceObjectApiName, syncPloyDetailSnapshotId, message.getDetailObjectDatasMap(), sourceEventType, destEventType,
//                            message.getSourceTenantType(), message.getDestTenantType());
//                }
//                syncDataManager
//                        .createSyncData(tenantId, syncDataId, dataMappingData.getId(), message.getSourceEventType(), message.getSourceTenantType(), sourceData, null, SyncDataStatusEnum.WAITTING.getStatus(),
//                                syncPloyDetailSnapshotId, dataMappingData.getDestDataId(), destEventType, destObjectApiName, destTenantId, message.getDestTenantType(), sourceDetailSyncDataIds, null, message.getMasterMappingsData());
//                //发送一次更新的事件
                dispatcherSendDelay(syncDataContextEvent);
                log.info("Update-SyncData is doing,mapping={}", dataMappingData);
                return syncDataContextEvent.stop("Update-SyncData is doing");
            }
        }


        /**
         * 新增或其他系统允许补齐从对象则进行补齐操作
         * 目标事件类型是新增才会将从对象数据也带过去，因为假设是更新事件，说明目标从对象存在，那目标事件从对象数据不存在才会是添加操作。
         * 因此，非新增事件就不必带从对象数据过去了，只更新同步对象（可主可从）数据
         */

        //新增 或 目标为ERP系统 或 目标对象配置需要填充，则填充明细
        boolean needFillUpDetailObject = destEventType == EventTypeEnum.ADD.getType()
                || TenantType.ERP.equals(syncDataContextEvent.getDestTenantType())
                || configCenterConfig.getUpdateCrmMasterDetailTogetherObjList(tenantId).contains(syncDataContextEvent.getDestObjectApiName());
        if (needFillUpDetailObject) {
            //TODO 从对象的时候，masterMappingData是空的
            sourceDetailSyncDataIds = createDetailSyncDataAndMapping(tenantId,syncDataContextEvent.getSourceContextUserId(), sourceObjectApiName, syncPloyDetailSnapshotId, syncDataContextEvent.getDetailObjectDatasMap(), sourceEventType, destEventType,
                    syncDataContextEvent.getSourceTenantType(), syncDataContextEvent.getDestTenantType(),syncDataContextEvent.getDataReceiveType(),syncDataContextEvent.getSourceMasterId(),syncDataContextEvent.getDataVersion());
        }
        SyncDataEntity dataEntity = syncDataManager
                .createSyncData(tenantId,syncDataContextEvent.getSourceContextUserId(), syncDataId, dataMappingData.getId(), sourceEventType, syncDataContextEvent.getSourceTenantType(), sourceData, SyncDataStatusEnum.BE_PROCESS.getStatus(), syncPloyDetailSnapshotId,
                        destDataId, destEventType, destObjectApiName, destTenantId, syncDataContextEvent.getDestTenantType(), sourceDetailSyncDataIds, syncDataContextEvent.getMasterMappingsData(),syncDataContextEvent.getDataReceiveType(),syncDataContextEvent.getDataVersion());
        syncDataContextEvent.setSyncDataData(BeanUtil.deepCopy(dataEntity, SyncDataData.class));
//        doProcessMqData.setDestMasterDataId(message.getDestMasterDataId());
//        doProcessMqData.setDestMasterObjectApiName(message.getDestMasterObjectApiName());
//        dataCenterMqProducer.sendDoProcess(doProcessMqData);
        return syncDataContextEvent.next();
    }

    private String invalidDetailIds(SyncDataContextEvent message, String tenantId, String syncPloyDetailSnapshotId, String sourceObjectApiName, StringBuilder remark) {
        for (Entry<String, List<ObjectData>> entry : message.getDetailObjectDatasMap().entrySet()) {
            String detailApiName = entry.getKey();
            List<ObjectData> details = entry.getValue();
            Set<String> findDupDetailIds = new HashSet<>(64);
            List<String> dupDetailIds = details.stream()
                    .map(ObjectData::getId)
                    .filter(v -> !findDupDetailIds.add(v))
                    .collect(Collectors.toList());
            if (!dupDetailIds.isEmpty()) {
                log.info("dump detailIds,message:{}", message);
                remark.append(i18NStringManager.getByEi(I18NStringEnum.s918,tenantId));
                remark.append("[").append(detailApiName).append("]")
                        .append(i18NStringManager.getByEi(I18NStringEnum.s919,tenantId))
                        .append(dupDetailIds).append(i18NStringManager.getByEi(I18NStringEnum.s920,tenantId));
                return I18NStringEnum.s918.name();
            }
        }
        Map<String, List<String>> createdDetailData = getCreatedDetailSyncDataMapping(tenantId, sourceObjectApiName, syncPloyDetailSnapshotId, message.getDetailObjectDatasMap()
        );
        log.info("getCreatedDetailSyncDataMapping createdDetailData={}", createdDetailData);
        if (!CollectionUtils.isEmpty(createdDetailData)) {
            remark.append(i18NStringManager.getByEi(I18NStringEnum.s921,tenantId)+",");
            for (String objApiName : createdDetailData.keySet()) {
                List<String> ids = createdDetailData.get(objApiName);
                if (StringUtils.isNotBlank(remark.toString())) {
                    remark.append(System.lineSeparator());
                }
                remark.append(i18NStringManager.getByEi2(I18NStringEnum.s922.getI18nKey(),
                        tenantId,
                        String.format(I18NStringEnum.s922.getI18nValue(), objApiName,ids.toString()),
                        Lists.newArrayList(objApiName,ids.toString())));
            }
            return I18NStringEnum.s921.name();
        }
        return null;
    }

    private Map<String, List<String>> getCreatedDetailSyncDataMapping(String tenantId, String sourceObjectApiName, String syncPloyDetailSnapshotId, Map<String, List<ObjectData>> detailObjectDatasMap) {
        Map<String, List<String>> result = Maps.newHashMap();
        SyncPloyDetailSnapshotEntity syncPloyDetailSnapshotEntity = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId, syncPloyDetailSnapshotId);
        SyncPloyDetailData syncPloyDetailData = syncPloyDetailSnapshotEntity.getSyncPloyDetailData();
        if (syncPloyDetailData == null || !sourceObjectApiName.equals(syncPloyDetailSnapshotEntity.getSourceObjectApiName())) {
            return result;
        }
        String destTenantId = syncPloyDetailSnapshotEntity.getDestTenantId();
        for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : syncPloyDetailData.getDetailObjectMappings()) {
            String detailDestObjectApiName = detailObjectMapping.getDestObjectApiName();
            String detailSourceObjectApiName = detailObjectMapping.getSourceObjectApiName();
            if (!CollectionUtils.isEmpty(detailObjectDatasMap.get(detailSourceObjectApiName))) {
                for (ObjectData objectData : detailObjectDatasMap.get(detailSourceObjectApiName)) {
                    boolean isExist = syncDataMappingManager.existByTwoWay(tenantId, detailSourceObjectApiName, objectData.getId(), detailDestObjectApiName);
                    if (isExist) {
                        if (result.containsKey(detailSourceObjectApiName)) {
                            result.get(detailSourceObjectApiName).add(objectData.getId());
                        } else {
                            result.put(detailSourceObjectApiName, Lists.newArrayList(objectData.getId()));
                        }
                    }
                }
            }
        }
        return result;
    }

    private boolean checkDetailDataToProcessed(ObjectData sourceData, String syncPloyDetailSnapshotId) {
        String objectApiName = sourceData.getApiName();
        SyncPloyDetailSnapshotEntity syncPloyDetailSnapshotEntity = syncPloyDetailSnapshotManager.getEntryBySnapshotId(sourceData.getTenantId(), syncPloyDetailSnapshotId);
        if (syncPloyDetailSnapshotEntity.getSourceObjectApiName().equals(objectApiName)) {
            //相等则是主对象，不用判断
            return true;
        }
        DetailObjectMappingsData detailObjectMappingData = syncPloyDetailSnapshotEntity.getSyncPloyDetailData().getDetailObjectMappings();
        if (detailObjectMappingData == null || detailObjectMappingData.isEmpty()) {
            return false;
        }
        String masterFieldApiName = null;
        for (DetailObjectMappingsData.DetailObjectMappingData detailMapping : detailObjectMappingData) {
            if (!detailMapping.getSourceObjectApiName().equals(objectApiName)) {
                continue;
            }
            for (FieldMappingData fieldMapping : detailMapping.getFieldMappings()) {
                if (fieldMapping.getSourceType().equals(FieldTypeContants.MASTER_DETAIL)) {
                    masterFieldApiName = fieldMapping.getSourceApiName();
                    break;
                }
            }
            break;
        }
        if (StringUtils.isNotEmpty(masterFieldApiName)) {
            Object mastId = sourceData.get(masterFieldApiName);
            return mastId != null;
        }
        return false;
    }

    private Map<String, List<String>> createDetailSyncDataAndMapping(String tenantId,String sourceContextUserId, String sourceObjectApiName, String syncPloyDetailSnapshotId, Map<String, List<ObjectData>> detailObjectDatasMap,
                                                                     Integer sourceEventType, Integer destEventType, Integer sourceTenantType, Integer destTenantType,Integer dataReceiveType,String  sourceMasterId,Long dataVersion) {
        Map<String, List<String>> sourceDetailSyncDataIdMap = new HashMap<>();
        SyncPloyDetailSnapshotEntity syncPloyDetailSnapshotEntity = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId, syncPloyDetailSnapshotId);
        SyncPloyDetailData syncPloyDetailData = syncPloyDetailSnapshotEntity.getSyncPloyDetailData();
        if (syncPloyDetailData == null || !sourceObjectApiName.equals(syncPloyDetailSnapshotEntity.getSourceObjectApiName())) {
            return sourceDetailSyncDataIdMap;
        }
        String destTenantId = syncPloyDetailSnapshotEntity.getDestTenantId();
        for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : syncPloyDetailData.getDetailObjectMappings()) {
            String destObjectApiName = detailObjectMapping.getDestObjectApiName();
            String detailObjectApiName = detailObjectMapping.getSourceObjectApiName();
            List<String> sourceDetailSyncDataIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(detailObjectDatasMap.get(detailObjectApiName))) {
                for (ObjectData objectData : detailObjectDatasMap.get(detailObjectApiName)) {
                    String detailSyncDataId = new ObjectId().toString();
                    SyncDataMappingsEntity mappingsEntity = syncDataMappingManager
                            .getOrCreateSyncDataMappingByTwoWay(tenantId, detailSyncDataId, SyncDataStatusEnum.BE_PROCESS.getStatus(), destObjectApiName, destTenantId, objectData, true,sourceMasterId).getSourcedData();
                    sourceDetailSyncDataIds.add(detailSyncDataId);
                    syncDataManager
                            .createSyncData2(tenantId,sourceContextUserId, detailSyncDataId, mappingsEntity.getId(), sourceEventType, sourceTenantType, objectData, SyncDataStatusEnum.BE_PROCESS.getStatus(), syncPloyDetailSnapshotId,
                                    mappingsEntity.getDestDataId(), destEventType, destObjectApiName, destTenantId, destTenantType, null, null, null, dataReceiveType,dataVersion);
                }
            }
            sourceDetailSyncDataIdMap.put(detailObjectApiName, sourceDetailSyncDataIds);
        }
        return sourceDetailSyncDataIdMap;
    }

    /**
     * 同步前执行自定义函数，校验是否同步该数据
     */
    @NotNull
    private SyncDataContextEvent executeCustomFunction(final SyncDataContextEvent syncDataContextEvent) {
        if (!syncDataContextEvent.isSuccess()) {
            return syncDataContextEvent;
        }
        if (StringUtils.isBlank(syncDataContextEvent.getSyncPloyDetailSnapshotId())) {
            return syncDataContextEvent;
        }
        SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(syncDataContextEvent.getSourceData().getTenantId(), syncDataContextEvent.getSyncPloyDetailSnapshotId()).getData();
        if (syncPloyDetailSnapshotData == null) {
            return syncDataContextEvent;
        }
        String functionApiName = syncPloyDetailSnapshotData.getSyncPloyDetailData().getBeforeFuncApiName();
        if (StringUtils.isNotBlank(functionApiName)) {
            String tenentId = syncPloyDetailSnapshotData.getSourceTenantId();
            ExecuteCustomFunctionArg executeCustomFunctionArg = new ExecuteCustomFunctionArg();
            executeCustomFunctionArg.setApiName(functionApiName);
            executeCustomFunctionArg.setNameSpace(CustomFunctionConstant.NAME_SPACE);
            executeCustomFunctionArg.setObjectData(syncDataContextEvent.getSourceData());
            Map<String, List<Map<String, Object>>> detailsObjectDataMap = BeanUtil.deepCopy(syncDataContextEvent.getDetailObjectDatasMap(), new TypeToken<Map<String, List<Map<String, Object>>>>() {
            }.getType());
            executeCustomFunctionArg.setDetails(detailsObjectDataMap);
//            Map<String, List<Map<String, Object>>> sourceDetailObjectDatasMap = BeanUtil.deepCopy(message.getDetailObjectDatasMap(), new TypeToken<Map<String, List<Map<String, Object>>>>() {
//            }.getType());
//            executeCustomFunctionArg.setDetails(sourceDetailObjectDatasMap);
            ExecuteCustomFunctionParameterData executeCustomFunctionParameterData = BeanUtil.deepCopy(syncDataContextEvent, ExecuteCustomFunctionParameterData.class);
            //复制不全，余下需set值
            executeCustomFunctionParameterData.setSourceTenantId(syncPloyDetailSnapshotData.getSourceTenantId());
            executeCustomFunctionParameterData.setSourceObjectApiName(syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceObjectApiName());
            executeCustomFunctionArg.setParameter(executeCustomFunctionParameterData);
            Result2<FunctionServiceExecuteReturnData> result = null;
            try {
                result = outerServiceFactory.get(syncDataContextEvent.getSourceTenantType()).executeCustomFunction(tenentId,
                        syncDataContextEvent.getDcId(),
                        executeCustomFunctionArg, null, CustomFunctionTypeEnum.BEFORE_FUNCTION, syncDataContextEvent.getSyncPloyDetailSnapshotId(), syncDataContextEvent.getSourceData().getName());

                try {//上报bizlog
                    SyncDataErrLog dumpLog = SyncDataErrLog.builder().tenantId(tenentId).appName("erpdss").objectApiName(executeCustomFunctionArg.getApiName()).build();
                    if (result.isSuccess()) {
                    } else {
                        dumpLog.setErrType(2);
                        dumpLog.setSyncErrMsg(result.getErrMsg());
                    }
                    if(SandboxUtil.isNotSandbox(eieaConverter, tenentId)) {
                        BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
                    }
                } catch (Exception e) {
                }

                if (!result.isSuccess()) {
                    log.warn("before sync execute custom function is fail, id ={}, executeCustomFunctionArg={}", syncDataContextEvent.getSourceData().getId(), executeCustomFunctionArg);
                    syncDataContextEvent.setErrCode(result.getIntErrCode());
                    syncDataContextEvent.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s923,tenentId) + result.getErrMsg());

                    return syncDataContextEvent;
                }
                //校验是否同步该数据
                if ((result.getData().getBoolean(CustomFunctionConstant.IS_EXEC) != null) && !result.getData().getBoolean(CustomFunctionConstant.IS_EXEC)) {
                    log.info("before sync execute custom function is disable, id ={} ,syncPloyDetailSnapshotData = {} ,executeCustomFunctionArg={}", syncDataContextEvent.getSourceData().getId(),
                            syncPloyDetailSnapshotData, executeCustomFunctionArg);
                    return syncDataContextEvent.stop(i18NStringManager.getByEi(I18NStringEnum.s915,syncDataContextEvent.getTenantId()));
                }
                //校验是否覆盖原数据数据
                if ((result.getData().getBoolean(CustomFunctionConstant.IS_COVER) == null) || !result.getData().getBoolean(CustomFunctionConstant.IS_COVER)) {
                    return syncDataContextEvent;
                }
                log.info("before sync execute custom function cover old data, resultData = {}", result.getData());
                //自定义函数返回主对象数据替换原主对象数据
                Map<String, Object> resultDataMap = BeanUtil.deepCopy(result.getData(), new TypeToken<Map<String, Object>>() {
                }.getType());
                ObjectData newObjectData = com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil.deepCopy(resultDataMap.get(CustomFunctionParameterEnum.OBJECT_DATA.getArgName()), new TypeToken<ObjectData>() {
                }.getType());
                if (newObjectData == null) {
                    return syncDataContextEvent;
                }
                ObjectData oldSourceData = syncDataContextEvent.getSourceData();
                //特殊信息不可覆盖
                for (Entry<String, Object> entry : newObjectData.entrySet()) {
                    if (("tenant_id".equals(entry.getKey()) || "object_describe_api_name".equals(entry.getKey()) && oldSourceData.get(entry.getKey()) != null)) {
                        newObjectData.put(entry.getKey(), oldSourceData.get(entry.getKey()));
                    }
                }
                syncDataContextEvent.setSourceData(newObjectData);
                //自定义函数返回从对象数据替换原从对象数据
                if (resultDataMap.get(CustomFunctionParameterEnum.DETAILS.getArgName()) == null) {
                    return syncDataContextEvent;
                }
                //key为objectApiName, List为从对象数据ObjectData
                Map<String, List<ObjectData>> newDetailsMap = com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil.deepCopy(resultDataMap.get(CustomFunctionParameterEnum.DETAILS.getArgName()), new TypeToken<Map<String, List<ObjectData>>>() {
                }.getType());
                Map<String, List<ObjectData>> oldDetailMap = syncDataContextEvent.getDetailObjectDatasMap();
                for (Entry<String, List<ObjectData>> entry : newDetailsMap.entrySet()) {
                    List<ObjectData> newDetailObjectDatas = entry.getValue();
                    List<ObjectData> oldDetailObjectDatas = oldDetailMap.get(entry.getKey());
                    Map<String, ObjectData> oldDetailsKeyIdMap = oldDetailObjectDatas.stream().collect(Collectors.toMap(ObjectData::getId, objectData -> objectData));
                    List<ObjectData> newDetailDataList = new ArrayList<>();
                    for (ObjectData newDetailObjectData : newDetailObjectDatas) {
                        ObjectData oldDetailObjectData = oldDetailsKeyIdMap.get(newDetailObjectData.getId());
                        if (oldDetailObjectData == null) {
                            continue;
                        }
                        for (Entry<String, Object> detailEntry : newDetailObjectData.entrySet()) {
                            if (("tenant_id".equals(detailEntry.getKey()) || "object_describe_api_name".equals(entry.getKey())) && (oldDetailObjectData.get(detailEntry.getKey()) != null)) {
                                newDetailObjectData.put(detailEntry.getKey(), oldDetailObjectData.get(detailEntry.getKey()));
                            }
                        }
                        newDetailDataList.add(newDetailObjectData);
                    }
                    newDetailsMap.put(entry.getKey(), newDetailDataList);
                }
                syncDataContextEvent.setDetailObjectDatasMap(newDetailsMap);
            } catch (Exception e) {
                //异常数据记录
                log.warn("before sync execute custom function is fail, executeCustomFunctionArg = " + executeCustomFunctionArg, e);
                syncDataContextEvent.setErrCode(ResultCodeEnum.CUSTOM_FUNC_EXECUTE_FAIL.getErrCode());
                syncDataContextEvent.setErrMsg(e.getMessage());
            }
        }
        return syncDataContextEvent;
    }

    /**
     * crm2erp明细不单独同步。触发主对象更新
     *
     * @param message
     */
    private void crmDetailTriggerMasterObjectUpdate(SyncDataContextEvent message) {
        //判断是从对象的事件
        String tenantId = message.getTenantId();
        FieldDescribe masterDetailField = crmMetaManager.getMasterDetailField(tenantId, message.getSourceData().getApiName());
        String mainApiName = masterDetailField.getTargetApiName();
        String mainDetailFieldApiName = masterDetailField.getApiName();
        String mainObjId = message.getSourceData().getString(mainDetailFieldApiName);
        //查找主数据最新的同步记录
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        GetByIdArg getByIdArg = new GetByIdArg();
        getByIdArg.setDescribeApiName(mainApiName);
        getByIdArg.setDataId(mainObjId);
        Result<ObjectDataGetByIdV3Result> masterCrmData = objectDataServiceV3.getById(headerObj, getByIdArg);
        //如果主数据作废或者删除了，getData会抛异常。如果差不到数据，data会为null
        if (!masterCrmData.isSuccess()
                || masterCrmData.getData() == null
                || masterCrmData.getData().getObjectData() == null) {
            return;
        }
        SyncDataContextEvent eventData = new SyncDataContextEvent();
        // 不保存字段信息,防止影响其他集成流
        ObjectData objectData = new ObjectData();
        objectData.putId(mainObjId);
        objectData.putApiName(mainApiName);
        objectData.putTenantId(tenantId);
        eventData.setSourceData(objectData);
        //一定是修改类型
        eventData.setSourceEventType(EventTypeEnum.UPDATE.getType());
        eventData.setSourceTenantType(TenantType.CRM);
        eventData.setDataReceiveType(DataReceiveTypeEnum.DETAIL_TRIGGER.getType());
        //只有crm方向的
        eventData.setSourceRemark(getSourceDataRemark(TenantType.CRM,tenantId,message.getSourceData()));
        String syncLogId = syncLogManager.getInitLogId(tenantId, objectData.getApiName());
        eventData.setSyncLogId(syncLogId);
        eventData.setForceSyncSnapshotIds(Lists.newArrayList(message.getSyncPloyDetailSnapshotId()));
        //不能设置快照Id，不然可能会修改了策略不生效。
        Result2<Void> voidResult = eventTriggerService.batchSendEventData2DispatcherMqByContext(Lists.newArrayList(eventData));
    }
    private String getSourceDataRemark(Integer sourceTenantType, String tenantId, ObjectData sourceData) {
        String name = sourceData.getName() == null ? sourceData.getId() : sourceData.getName();
        String objectApiName = sourceData.getApiName();
        StringBuilder sb = new StringBuilder();
        try {
            if (TenantType.CRM.equals(sourceTenantType)) {
                ObjectDescribe objectDescribe = crmMetaManager.getObjectDescribe(tenantId, objectApiName);
                if (objectDescribe == null) {
                    sb.append(objectApiName);
                } else {
                    sb.append(objectDescribe.getDisplayName());
                }
            } else {
                ErpObjectEntity erpObject = erpObjManager.getErpObj(tenantId, objectApiName);
                if (erpObject == null) {
                    sb.append(objectApiName);
                } else {
                    sb.append(erpObject.getErpObjectName());
                }
            }
        } catch (Exception e) {
            sb.append(objectApiName);
            log.warn("getSourceDataRemark Exception e={}", e);
        }
        sb.append("[").append(name).append("]").append(I18NStringEnum.s5107.getText()).append(I18NStringEnum.s5108.getText());
        return sb.toString();
    }

    /**
     * waiting的数据触发更新
     */
  private void dispatcherSendDelay(SyncDataContextEvent message){
      SyncDataContextEvent eventData = new SyncDataContextEvent();
      String tenantId=message.getTenantId();
      ObjectData objectData = new ObjectData();
      //erp-crm的主，updatedata是空的
      if(ObjectUtils.isNotEmpty(message.getUpdateData())){
          objectData.putAll(message.getUpdateData());
      }else{
          objectData.putAll(message.getSourceData());
      }

      eventData.setSourceData(objectData);
      //一定是修改类型
      eventData.setSourceEventType(message.getSourceEventType());
      eventData.setSourceTenantType(message.getSourceTenantType());
      eventData.setPloyDetailSnapshotId(message.getSyncPloyDetailSnapshotId());
      Long aggregationTime = configCenterConfig.getPublicLongConfig(tenantId, "ALL", "ALL", TenantConfigurationTypeEnum.DELAY_DISPATCHER_TIME.name(), ConfigCenterConfig.WAITING_DELAY_DISPATCHER_TIME);
      eventData.setDelayDispatcherTime(aggregationTime);
      String syncLogId = syncLogManager.getInitLogId(message.getTenantId(), objectData.getApiName());
      eventData.setSyncLogId(syncLogId);
      log.info("repeat send eventData:{}", JSONObject.toJSONString(eventData));
      Result2<Void> voidResult = eventTriggerService.batchSendEventData2DispatcherMqByContext(Lists.newArrayList(eventData));

  }

    private void setDataVersion(String tenantId,SyncDataContextEvent eventData, ErpTempData erpTempData) {
        ObjectData sourceData = eventData.getSourceData();
        if(eventData.getSourceTenantType()==TenantType.CRM){
            if(sourceData.getVersion()!=null){
                erpTempData.setLastSyncTime(sourceData.getVersion());
            }else{
                Result2<ObjectData> objectDataResult2 = outerServiceFactory.get(TenantType.CRM).getObjectData(tenantId, TenantType.CRM, sourceData.getApiName(), sourceData.getId());
                if(objectDataResult2!=null&&objectDataResult2.isSuccess()&&objectDataResult2.getData()!=null){
                    erpTempData.setLastSyncTime(objectDataResult2.getData().getVersion());
                }
            }
        }else {
            erpTempData.setLastSyncTime(eventData.getDataVersion());
        }
    }

}
