package com.fxiaoke.open.erpsyncdata.converter.fieldconvert;

import com.fxiaoke.open.erpsyncdata.common.constant.FieldMappingTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.util.FieldTypeUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SourceQuoteValueFieldConverter implements FieldConverter {
    @Autowired
    private OuterServiceFactory outerServiceFactory;

    @Override
    public Object convert(String tenantId, ObjectData sourceData, Integer destEventType, String destTenantId, FieldMappingData fieldMappingData, Integer destTenantType, Integer sourceTenantType, String destObjectApiName) {
        Object sourceValue = sourceData.get(fieldMappingData.getSourceApiName());
        if (sourceValue == null) {
            return null;
        }
        String destType = fieldMappingData.getDestType();
        String sourceType = fieldMappingData.getSourceType();
        String fieldName = fieldMappingData.getValue();
        if (sourceType.equals(FieldType.OBJECT_REFERENCE) && sourceValue != null && FieldTypeUtil.isStringType(destType)) {
            return sourceData.getReferName(fieldMappingData.getSourceApiName());
        }
        if (sourceType.equals(FieldType.EMPLOYEE) && sourceValue != null && FieldTypeUtil.isStringType(destType)) {
            String name = sourceData.getMapValue(fieldMappingData.getSourceApiName(), fieldName);
            if (!StringUtils.isEmpty(name)) {
                return name;
            }
            List<String> employeeIds = (List<String>) sourceValue;
            if (employeeIds.isEmpty()) {
                return null;
            }
            Map<String, String> names = outerServiceFactory.get(sourceTenantType).batchGetEmployeeFieldValue(sourceData.getTenantId(), sourceTenantType, employeeIds, fieldName).getData();
            return names.get(employeeIds.get(0));
        }
        return sourceValue;
    }

    @Override
    public Integer getFieldMappingType() {
        return FieldMappingTypeEnum.SOUCRE_QUOTE_VAULE.getType();
    }
}
