package com.fxiaoke.open.erpsyncdata.converter.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.data.bizlog.SyncDataErrLog;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.common.util.AssertUtil;
import com.fxiaoke.open.erpsyncdata.common.util.BeanUtil2;
import com.fxiaoke.open.erpsyncdata.common.util.SandboxUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.SyncDataThreadHolder;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.PloyDetailNodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.MasterMappingsData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SimpleSyncData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataData;
import com.fxiaoke.open.erpsyncdata.preprocess.service.OverrideOuterService;
import com.fxiaoke.ps.ProtostuffUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import java.util.*;

@Component
@Slf4j
public class SyncDataManager {
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private SyncDataFixDao syncDataDao;
    @Autowired
    private SyncDataThreadHolder syncDataThreadHolder;
    @Autowired
    private OverrideOuterService overrideOuterService;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private PloyBreakManager ployBreakManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private EIEAConverter eieaConverter;
    private static List<String> fieldList = Lists.newArrayList("object_describe_api_name", "_id", "name", "tenant_id", "owner");

    /**
     * 精简字段
     *
     * @param syncDataEntity
     */
    public void cutDownSourceDataFieldList(SyncDataEntity syncDataEntity, SyncPloyDetailSnapshotEntity syncPloyDetailSnapshotEntity) {
        if (syncDataEntity == null || StringUtils.isBlank(syncDataEntity.getSyncPloyDetailSnapshotId())
                || syncDataEntity.getSourceData() == null || CollectionUtils.isEmpty(syncDataEntity.getSourceData().keySet())) {
            return;
        }
        Map<String, Set<String>> obj2Field = getObjFieldApiName(syncPloyDetailSnapshotEntity);
        ObjectData sourceData = syncDataEntity.getSourceData();
        if (obj2Field != null && obj2Field.keySet().contains(syncDataEntity.getSourceObjectApiName())) {
            sourceData = new ObjectData();
            Set<String> objFieldApiName = obj2Field.get(syncDataEntity.getSourceObjectApiName());
            for (String fieldApiName : objFieldApiName) {
                if (syncDataEntity.getSourceData().containsKey(fieldApiName)) {
                    //null值也支持传输
                    sourceData.put(fieldApiName, syncDataEntity.getSourceData().get(fieldApiName));
                }
            }
        }
        syncDataEntity.setSourceData(sourceData);
    }

    public Map<String, Set<String>> getObjFieldApiName(SyncPloyDetailSnapshotEntity syncPloyDetailSnapshotEntity) {
        SyncPloyDetailData syncPloyDetailData = syncPloyDetailSnapshotEntity.getSyncPloyDetailData();
        Set<String> sourceMasterField = Sets.newHashSet();
        //主数据数据
        for (FieldMappingData fieldMappingData : syncPloyDetailData.getFieldMappings()) {
            if (StringUtils.isEmpty(fieldMappingData.getSourceApiName())) {
                continue;
            }
            if (StringUtils.isNotBlank(fieldMappingData.getSourceQuoteRealField())) {//引用字段
                sourceMasterField.add(fieldMappingData.getSourceQuoteRealField());
            }
            sourceMasterField.add(fieldMappingData.getSourceApiName());
        }
        sourceMasterField.addAll(fieldList);
        //主数据数据范围
        if (syncPloyDetailData.getSyncConditions().getFilters() != null) {
            for (List<FilterData> filterDataList : syncPloyDetailData.getSyncConditions().getFilters()) {
                if (filterDataList != null) {
                    for (FilterData filterData : filterDataList) {
                        sourceMasterField.add(filterData.getFieldApiName());
                    }
                }
            }
        }
        Map<String, Set<String>> sourceDetailFieldMap = Maps.newHashMap();
        //明细数据
        for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : syncPloyDetailData.getDetailObjectMappings()) {
            Set<String> sourceDetailFieldSet = Sets.newHashSet();

            for (FieldMappingData fieldMappingData : detailObjectMappingData.getFieldMappings()) {
                if (StringUtils.isEmpty(fieldMappingData.getSourceApiName())) {
                    continue;
                }
                if (StringUtils.isNotBlank(fieldMappingData.getSourceQuoteRealField())) {//引用字段
                    sourceDetailFieldSet.add(fieldMappingData.getSourceQuoteRealField());
                }
                sourceDetailFieldSet.add(fieldMappingData.getSourceApiName());
            }
            sourceDetailFieldSet.addAll(fieldList);
            sourceDetailFieldMap.put(detailObjectMappingData.getSourceObjectApiName(), sourceDetailFieldSet);
        }
        //明细数据范围
        for (SyncConditionsData syncConditionsData : syncPloyDetailData.getDetailObjectSyncConditions()) {
            if (syncConditionsData.getFilters() != null) {
                for (List<FilterData> filterDataList : syncConditionsData.getFilters()) {
                    if (filterDataList != null) {
                        for (FilterData filterData : filterDataList) {
                            sourceDetailFieldMap.get(syncConditionsData.getApiName()).add(filterData.getFieldApiName());
                        }
                    }
                }
            }
        }
        //其他节点节点
        if (syncPloyDetailData.getIntegrationStreamNodes() != null) {
            //主数据范围-查询crm节点
            if(syncPloyDetailData.getIntegrationStreamNodes().getSyncConditionsQueryDataNode()!=null){
               IntegrationStreamNodesData.SyncConditionsQueryDataNode syncConditionsQueryDataNode= syncPloyDetailData.getIntegrationStreamNodes().getSyncConditionsQueryDataNode();
               if(CollectionUtils.isNotEmpty(syncConditionsQueryDataNode.getQueryObjectMappingData())){
                   for(QueryObjectMappingData queryObjectMappingData:syncConditionsQueryDataNode.getQueryObjectMappingData()){
                       QueryObjectOrFilterFieldMappingsData queryFieldMappings = queryObjectMappingData.getQueryFieldMappings();
                       if(CollectionUtils.isNotEmpty(queryFieldMappings)){
                           for (QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingData filterDatas : queryFieldMappings) {
                               if (CollectionUtils.isNotEmpty(filterDatas)) {
                                   for (FilterData filterData : filterDatas) {
                                       if (CollectionUtils.isNotEmpty(filterData.getFieldValue()) && filterData.getFieldValue().get(0) != null) {
                                           sourceMasterField.add(filterData.getFieldValue().get(0).toString());
                                       }
                                   }
                               }
                           }
                       }
                   }
               }
            }
            //通过源查询crm节点
            if (syncPloyDetailData.getIntegrationStreamNodes().getQueryCrmObject2DestNodeBySource() != null) {
                IntegrationStreamNodesData.QueryCrmObject2DestNode queryCrmObject2DestNodeBySource = syncPloyDetailData.getIntegrationStreamNodes().getQueryCrmObject2DestNodeBySource();
                //主
                if (CollectionUtils.isNotEmpty(queryCrmObject2DestNodeBySource.getQueryObjectToDestObject())) {
                    List<QueryObjectToDestObjectData> queryObjectToDestObject = syncPloyDetailData.getIntegrationStreamNodes().getQueryCrmObject2DestNodeBySource().getQueryObjectToDestObject();
                    for (QueryObjectToDestObjectData queryObjectToDestObjectData : queryObjectToDestObject) {
                        if (queryObjectToDestObjectData.getQueryObjectMappingData() != null && CollectionUtils.isNotEmpty(queryObjectToDestObjectData.getQueryObjectMappingData().getQueryFieldMappings())) {
                            QueryObjectOrFilterFieldMappingsData queryFieldMappings = queryObjectToDestObjectData.getQueryObjectMappingData().getQueryFieldMappings();
                            for (QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingData filterDatas : queryFieldMappings) {
                                if (CollectionUtils.isNotEmpty(filterDatas)) {
                                    for (FilterData filterData : filterDatas) {
                                        if (CollectionUtils.isNotEmpty(filterData.getFieldValue()) && filterData.getFieldValue().get(0) != null) {
                                            sourceMasterField.add(filterData.getFieldValue().get(0).toString());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                //从
                if (CollectionUtils.isNotEmpty(queryCrmObject2DestNodeBySource.getDetailQueryData2DestDataMapping())) {
                    DetailQueryObjectMappingsData detailQueryData2DestDataMapping = syncPloyDetailData.getIntegrationStreamNodes().getQueryCrmObject2DestNodeBySource().getDetailQueryData2DestDataMapping();
                    for (List<QueryObjectToDestObjectData> list : detailQueryData2DestDataMapping) {
                        if (CollectionUtils.isNotEmpty(list)) {
                            for (QueryObjectToDestObjectData queryObjectToDestObjectData : list) {
                                if (queryObjectToDestObjectData.getQueryObjectMappingData() != null && CollectionUtils.isNotEmpty(queryObjectToDestObjectData.getQueryObjectMappingData().getQueryFieldMappings())) {
                                    String sourceObjApiName = queryObjectToDestObjectData.getQueryObjectMappingData().getDestObjectApiName();
                                    QueryObjectOrFilterFieldMappingsData queryFieldMappings = queryObjectToDestObjectData.getQueryObjectMappingData().getQueryFieldMappings();
                                    for (QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingData filterDatas : queryFieldMappings) {
                                        if (CollectionUtils.isNotEmpty(filterDatas)) {
                                            for (FilterData filterData : filterDatas) {
                                                if (sourceDetailFieldMap.get(sourceObjApiName) != null && CollectionUtils.isNotEmpty(filterData.getFieldValue()) && filterData.getFieldValue().get(0) != null) {
                                                    sourceDetailFieldMap.get(sourceObjApiName).add(filterData.getFieldValue().get(0).toString());
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

            }
            //查询中间表节点
            if (syncPloyDetailData.getIntegrationStreamNodes().getCheckSyncDataMappingNode() != null) {
                IntegrationStreamNodesData.CheckSyncDataMappingNode checkSyncDataMappingNode = syncPloyDetailData.getIntegrationStreamNodes().getCheckSyncDataMappingNode();
                //主
                if (checkSyncDataMappingNode.getQueryObjectMappingData() != null && CollectionUtils.isNotEmpty(checkSyncDataMappingNode.getQueryObjectMappingData().getQueryFieldMappings())) {
                    QueryObjectOrFilterFieldMappingsData queryFieldMappings = checkSyncDataMappingNode.getQueryObjectMappingData().getQueryFieldMappings();
                    for (QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingData filterDatas : queryFieldMappings) {
                        if (CollectionUtils.isNotEmpty(filterDatas)) {
                            for (FilterData filterData : filterDatas) {
                                if (CollectionUtils.isNotEmpty(filterData.getFieldValue()) && filterData.getFieldValue().get(0) != null) {
                                    sourceMasterField.add(filterData.getFieldValue().get(0).toString());
                                }
                            }
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(checkSyncDataMappingNode.getSource2SyncDataMapping())) {
                    for (DetailObjectIdFieldMappingsData.DetailObjectIdFieldMappingData data : checkSyncDataMappingNode.getSource2SyncDataMapping()) {
                        if (syncPloyDetailSnapshotEntity.getSyncPloyDetailData().getSourceObjectApiName().equals(data.getSourceObjectApiName())) {
                            sourceMasterField.add(data.getSourceApiName());
                        }
                    }
                }
                //从
                if (CollectionUtils.isNotEmpty(checkSyncDataMappingNode.getDetailCheckSyncDataMappingData())) {
                    for (DetailQueryObject2SyncDataMappingsData.DetailQueryObject2SyncDataMappingData detail : checkSyncDataMappingNode.getDetailCheckSyncDataMappingData()) {
                        String sourceObjApiName = detail.getQueryObjectMappingData().getDestObjectApiName();
                        if (detail.getQueryObjectMappingData() != null && CollectionUtils.isNotEmpty(detail.getQueryObjectMappingData().getQueryFieldMappings())) {
                            QueryObjectOrFilterFieldMappingsData queryFieldMappings = detail.getQueryObjectMappingData().getQueryFieldMappings();
                            for (QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingData filterDatas : queryFieldMappings) {
                                if (CollectionUtils.isNotEmpty(filterDatas)) {
                                    for (FilterData filterData : filterDatas) {
                                        if (CollectionUtils.isNotEmpty(filterData.getFieldValue()) && filterData.getFieldValue().get(0) != null) {
                                            if (sourceDetailFieldMap.get(sourceObjApiName) != null && CollectionUtils.isNotEmpty(filterData.getFieldValue()) && filterData.getFieldValue().get(0) != null) {
                                                sourceDetailFieldMap.get(sourceObjApiName).add(filterData.getFieldValue().get(0).toString());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (CollectionUtils.isNotEmpty(detail.getSource2SyncDataMapping())) {
                            for (DetailObjectIdFieldMappingsData.DetailObjectIdFieldMappingData data : detail.getSource2SyncDataMapping()) {
                                if (sourceObjApiName.equals(data.getSourceObjectApiName()) && sourceDetailFieldMap.get(sourceObjApiName) != null
                                        && StringUtils.isNotBlank(data.getSourceApiName())) {
                                    sourceDetailFieldMap.get(sourceObjApiName).add(data.getSourceApiName());
                                }
                            }
                        }
                    }
                }
            }
        }
        Map<String, Set<String>> all = Maps.newHashMap();
        all.put(syncPloyDetailSnapshotEntity.getSyncPloyDetailData().getSourceObjectApiName(), sourceMasterField);
        all.putAll(sourceDetailFieldMap);
        return all;
    }

    public SyncDataEntity createSyncData2(String tenantId,
                                          String sourceContextUserId,
                                          String syncDataId,
                                          String syncDataMappingId,
                                          Integer sourceEventType,
                                          Integer sourceTenantType,
                                          ObjectData sourceData,
                                          Integer status,
                                          String syncPloyDetailSnapshotId,
                                          String destDataId,
                                          Integer destEventType,
                                          String destObjectApiName,
                                          String destTenantId,
                                          Integer destTenantType,
                                          MasterMappingsData masterMappingsData,
                                          String remark, String errorCode, Integer dataReceiveType,Long dataVersion) {
        return this
                .createSyncData(tenantId,sourceContextUserId, syncDataId, syncDataMappingId, sourceEventType, sourceTenantType, sourceData, null, status, syncPloyDetailSnapshotId, destDataId, destEventType, destObjectApiName, destTenantId,
                        destTenantType, null, remark, errorCode, masterMappingsData,dataReceiveType,dataVersion);
    }

    public SyncDataEntity createSyncData(String tenantId,
                                         String sourceContextUserId,
                                         String syncDataId,
                                         String syncDataMappingId,
                                         Integer sourceEventType,
                                         Integer sourceTenantType,
                                         ObjectData sourceData,
                                         ObjectData destData,
                                         Integer status,
                                         String syncPloyDetailSnapshotId,
                                         String destDataId,
                                         Integer destEventType,
                                         String destObjectApiName,
                                         String destTenantId,
                                         Integer destTenantType,
                                         Map<String, List<String>> sourceDetailSyncDataIds,
                                         String remark,
                                         String errorCode,
                                         MasterMappingsData masterMappingsData, Integer dataReceiveType,Long dataVersion) {
        //获取策略快照
        SyncPloyDetailSnapshotEntity syncPloyDetailSnapshotEntity = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId, syncPloyDetailSnapshotId);
        return this.createSyncDataWithSnapshotEntity(tenantId,sourceContextUserId, syncDataId, syncDataMappingId, sourceEventType, sourceTenantType, sourceData, destData, status, syncPloyDetailSnapshotEntity, destDataId, destEventType, destObjectApiName, destTenantId,
                destTenantType, sourceDetailSyncDataIds, remark, errorCode, masterMappingsData,dataReceiveType,dataVersion);
    }

    @Transactional
    public SyncDataEntity createSyncDataWithSnapshotEntity(String tenantId,
                                                           String sourceContextUserId,
                                                           String syncDataId,
                                                           String syncDataMappingId,
                                                           Integer sourceEventType,
                                                           Integer sourceTenantType,
                                                           ObjectData sourceData,
                                                           ObjectData destData,
                                                           Integer status,
                                                           SyncPloyDetailSnapshotEntity syncPloyDetailSnapshotEntity,
                                                           String destDataId,
                                                           Integer destEventType,
                                                           String destObjectApiName,
                                                           String destTenantId,
                                                           Integer destTenantType,
                                                           Map<String, List<String>> sourceDetailSyncDataIds,
                                                           String remark,
                                                           String errorCode, MasterMappingsData masterMappingsData,
                                                           Integer dataReceiveType,Long dataVersion) {
        AssertUtil.assertIfEmpty("sourceData.id", sourceData.getId());
        AssertUtil.assertIfEmpty("sourceData.tenantId", sourceData.getTenantId());
        AssertUtil.assertIfEmpty("sourceData.apiName", sourceData.getApiName());
        AssertUtil.assertIfEmpty("destObjectApiName", destObjectApiName);
        AssertUtil.assertIfEmpty("destTenantId", destTenantId);
        long now = System.currentTimeMillis();
        SyncDataEntity dataEntity = new SyncDataEntity();
        dataEntity.setId(syncDataId);
        dataEntity.setSourceTenantType(sourceTenantType);
        dataEntity.setDestTenantType(destTenantType);
        dataEntity.setSourceDataId(sourceData.getId());
        dataEntity.setSourceDataName(sourceData.getName());
        dataEntity.setSourceEventType(sourceEventType);
        //erp同步中source和dest的企业id是一样的
        dataEntity.setTenantId(tenantId);
        dataEntity.setSourceTenantId(sourceData.getTenantId());
        dataEntity.setSourceObjectApiName(sourceData.getApiName());
        dataEntity.setSourceData(sourceData);
        dataEntity.setDestData(destData);
        dataEntity.setDestTenantId(destTenantId);
        dataEntity.setDestObjectApiName(destObjectApiName);
        dataEntity.setDestDataId(destDataId);
        dataEntity.setDestEventType(destEventType);
        dataEntity.setStatus(status);
        dataEntity.setRemark(remark);
        dataEntity.setErrorCode(errorCode);
        dataEntity.setSyncPloyDetailSnapshotId(syncPloyDetailSnapshotEntity.getId());
        dataEntity.setCreateTime(now);
        dataEntity.setUpdateTime(now);
        dataEntity.setIsDeleted(false);
        dataEntity.setSyncLogId(LogIdUtil.get());
        if(StringUtils.isNotBlank(sourceContextUserId)){
            dataEntity.setOperatorId(sourceContextUserId);
        }else{
            dataEntity.setOperatorId(sourceData.getLastModifiedBy());
        }
        dataEntity.setDataReceiveType(dataReceiveType);
        if(TenantType.CRM==sourceTenantType){
            if(sourceData.getVersion()!=null){
                dataEntity.getData().setDataVersion(sourceData.getVersion().toString());
            }
        }else{
            if(dataVersion!=null){
                dataEntity.getData().setDataVersion(dataVersion.toString());
            }
        }
        List<String> sourceDetailSyncDataIdList = null;

        if (sourceDetailSyncDataIds != null) {
            MapListStringData mapListStringData = new MapListStringData();
            mapListStringData.putAll(sourceDetailSyncDataIds);
            dataEntity.setSourceDetailSyncDataIds(mapListStringData);

            sourceDetailSyncDataIdList = new ArrayList<>();
            for (String key : sourceDetailSyncDataIds.keySet()) {
                sourceDetailSyncDataIdList.addAll(sourceDetailSyncDataIds.get(key));
            }
        }
        //删减字段
        this.cutDownSourceDataFieldList(dataEntity, syncPloyDetailSnapshotEntity);
        //不允许失败，失败会导致异常 调整为：失败不影响后面同步
        syncDataDao.setTenantId(tenantId).insertCache(dataEntity);

        //从对象关联主对象的源数据ID
        if (CollectionUtils.isNotEmpty(sourceDetailSyncDataIdList)) {
            syncDataMappingsDao.setTenantId(tenantId).batchUpdateMasterDataIdBySyncDataIdList(tenantId,
                    sourceDetailSyncDataIdList,
                    dataEntity.getSourceDataId());
        }

        if (status != SyncDataStatusEnum.WAITTING.getStatus()) {
            syncDataMappingsDao.setTenantId(tenantId).updateById(tenantId, syncDataMappingId,sourceData.getName(), syncDataId, status, sourceData.getVersion(), now);
        }

        //从对象关联主对象，更新从对象上的masterDataId字段
        if (masterMappingsData != null) {
            int count = syncDataMappingsDao.setTenantId(tenantId).batchUpdateMasterDataIdBySyncDataIdList(tenantId,
                    Lists.newArrayList(syncDataId),
                    masterMappingsData.getSourceDataId());
            log.info("SyncDataManager.createSyncData,masterMappingsData={},count={}", masterMappingsData, count);
        }
        return dataEntity;
    }

    public SyncDataEntity createSyncData(String tenantId,
                                         String sourceContextUserId,
                                         String syncDataId,
                                         String syncDataMappingId,
                                         Integer sourceEventType,
                                         Integer sourceTenantType,
                                         ObjectData sourceData,
                                         Integer status,
                                         String syncPloyDetailSnapshotId,
                                         String destDataId,
                                         Integer destEventType,
                                         String destObjectApiName,
                                         String destTenantId,
                                         Integer destTenantType,
                                         Map<String, List<String>> sourceDetailSyncDataIds,
                                         MasterMappingsData masterMappingsData,
                                         Integer dataReceiveType,Long dataVersion) {
        return this
                .createSyncData(tenantId,sourceContextUserId, syncDataId, syncDataMappingId, sourceEventType, sourceTenantType, sourceData, null, status, syncPloyDetailSnapshotId, destDataId, destEventType, destObjectApiName, destTenantId,
                        destTenantType, sourceDetailSyncDataIds, null, null, masterMappingsData,dataReceiveType,dataVersion);
    }

    public int updateDestEventTypeAndDestDataIdAndStatus(String tenantId, String syncDataMappingId, String syncDataId, Integer destEventType, String destDataId, Integer oldStatus, Integer newStatus, Long version, String snapId) {
        long now = System.currentTimeMillis();
        int success = syncDataDao.setTenantId(tenantId).updateDestEventTypeAndDestDataIdAndStatus(tenantId, syncDataId, destEventType, destDataId, oldStatus, newStatus, now);

        syncDataMappingsDao.setTenantId(tenantId).updateById(tenantId, syncDataMappingId,null, syncDataId, newStatus, version, now);
        if (SyncDataStatusEnum.isFailed(newStatus)) {
            ployBreakManager.incrFailedSyncDataNum(tenantId, syncDataId);
        }

        try {//上报bizlog
            SyncDataErrLog dumpLog = SyncDataErrLog.builder().tenantId(tenantId).appName("erpdss").objectApiName("").build();
            if (SyncDataStatusEnum.isFailed(newStatus)) {
                dumpLog.setErrType(0);
            } else {
                dumpLog.setErrType(-1);
                dumpLog.setSyncErrMsg(SyncDataStatusEnum.getNameByStatus(newStatus));
            }
            if(SandboxUtil.isNotSandbox(eieaConverter, tenantId)) {
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            }
        } catch (Exception e) {
        }

        return success;
    }

    public void updateToError(String tenantId, String syncDataId, Integer newStatus, String errMsg, String errCode) {
        String remark = errMsg;
        int success = syncDataDao.setTenantId(tenantId).updateStatus(tenantId, syncDataId, newStatus, remark, errCode);

        syncDataMappingsDao.setTenantId(tenantId).updateBySyncDataId(tenantId, syncDataId, newStatus, null, remark, System.currentTimeMillis());
        if (SyncDataStatusEnum.isFailed(newStatus)) {
            ployBreakManager.incrFailedSyncDataNum(tenantId, syncDataId);
        }

        try {//上报bizlog
            SyncDataErrLog dumpLog = SyncDataErrLog.builder().tenantId(tenantId).appName("erpdss").objectApiName("").build();
            if (SyncDataStatusEnum.isFailed(newStatus)) {
                dumpLog.setErrType(0);
            } else {
                dumpLog.setErrType(-1);
                dumpLog.setSyncErrMsg(SyncDataStatusEnum.getNameByStatus(newStatus));
            }
            if(SandboxUtil.isNotSandbox(eieaConverter, tenantId)) {
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            }
        } catch (Exception e) {
        }
    }
    public void updateNodeMsg(String tenantId, String sourceObjApiName,PloyDetailNodeEnum lastNodeName, String lastNodeStatus, String dataVersion) {
        syncDataDao.setTenantId(tenantId).updateNodeMsg(tenantId,sourceObjApiName,lastNodeName.getType(), lastNodeStatus, dataVersion,null,null,null,null);
    }
    public void updateReverseWriteFailed(String tenantId, String sourceObjApiName,PloyDetailNodeEnum lastNodeName, String lastNodeStatus, String dataVersion,String reverseWrite2CrmFailedRemark) {
        syncDataDao.setTenantId(tenantId).updateNodeMsg(tenantId,sourceObjApiName,lastNodeName.getType(), lastNodeStatus, dataVersion,true,reverseWrite2CrmFailedRemark,null,null);
    }
    public void updateAfterFuncFailed(String tenantId, String sourceObjApiName,PloyDetailNodeEnum lastNodeName, String lastNodeStatus, String dataVersion,String afterFuncFailedRemark) {
        syncDataDao.setTenantId(tenantId).updateNodeMsg(tenantId,sourceObjApiName,lastNodeName.getType(), lastNodeStatus, dataVersion,null,null,true,afterFuncFailedRemark);
    }
    public void updateNodeMsgBySyncDataId(String tenantId, String syncDataId,PloyDetailNodeEnum lastNodeName, String lastNodeStatus, String dataVersion) {
        syncDataDao.setTenantId(tenantId).updateNodeMsgBySyncDataId(tenantId,syncDataId,lastNodeName.getType(), lastNodeStatus, dataVersion);
    }

    /**
     * 更新状态
     *
     * @param tenantId
     * @param syncDataId
     * @param destDataName 可以为null，为null不更新
     * @param newStatus
     * @param errMsg
     * @param errCode
     */
    public void updateBySyncDataId(String tenantId, String syncDataId, String destDataName, Integer newStatus, String errMsg, String errCode) {
        String remark = errMsg;
        int success = syncDataDao.updateStatus(tenantId, syncDataId, newStatus, remark, errCode);
        syncDataMappingsDao.updateBySyncDataId(tenantId, syncDataId, newStatus, destDataName, remark, System.currentTimeMillis());
        if (SyncDataStatusEnum.isFailed(newStatus)) {
            ployBreakManager.incrFailedSyncDataNum(tenantId, syncDataId);
        }

        try {//上报bizlog
            SyncDataErrLog dumpLog = SyncDataErrLog.builder().tenantId(tenantId).appName("erpdss").objectApiName(destDataName).build();
            if (SyncDataStatusEnum.isFailed(newStatus)) {
                dumpLog.setErrType(0);
            } else {
                dumpLog.setErrType(-1);
                dumpLog.setSyncErrMsg(SyncDataStatusEnum.getNameByStatus(newStatus));
            }
            if(SandboxUtil.isNotSandbox(eieaConverter, tenantId)) {
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            }
        } catch (Exception e) {
        }
    }

    /**
     * 仅仅更新一下目标数据
     */
    public void updateDestData(String tenantId, String id, String destDataId, ObjectData destData, Integer oldStatus, Integer newStatus) {
        syncDataDao.updateDestData(tenantId, id, destDataId, destData, oldStatus, newStatus, System.currentTimeMillis());
        //不管SyncData是否成功
        //不在这里查主属性更新
//        String destDataName = getDestDataName(tenantId,destTenantType,  destData);
//        syncDataMappingsDao.setTenantId(tenantId).batchUpdateBySyncDataIdAdmin(tenantId, Lists.newArrayList(id), newStatus, destDataName,null, System.currentTimeMillis());
//        if (SyncDataStatusEnum.isFailed(newStatus)) {
//            ployBreakManager.incrFailedSyncDataNum(tenantId, id);
//        }
    }
    public void updateNeedReturnData(String tenantId, String id, ObjectData needReturnData) {
        syncDataDao.updateNeedReturnData(tenantId, id, needReturnData,System.currentTimeMillis());
    }

    @Nullable
    private String getDestDataName(String tenantId, Integer destTenantType, ObjectData destData) {
        if (destData == null) {
            return null;
        }
        String destDataName = destData.getName();
        if (TenantType.ERP.equals(destTenantType)) {
            //取目标主属性
            Map<String, String> objectMainAttribute = overrideOuterService.getObjectMainAttribute(tenantId);
            if (objectMainAttribute.containsKey(destData.getApiName())) {
                String mainAttributeField = objectMainAttribute.get(destData.getApiName());
                if (StringUtils.isNotBlank(destData.getString(mainAttributeField))) {
                    destDataName = destData.getString(mainAttributeField);
                }
            }
        }
        return destDataName;
    }

    public void updateStatus(String tenantId, String syncDataId, Integer newStatus, String remark, String errCode) {
        updateBySyncDataId(tenantId, syncDataId, null, newStatus, remark, errCode);
//        //作废数据成功之后，作废相反方向的数据
//        if (newStatus == SyncDataStatusEnum.WRITE_SUCCESS.getStatus()) {
//            invalidData(tenantId, ids);
//        }
    }

    public int updateStatusAndDestDataIdAndCreatedBySuccess(String tenantId, SimpleSyncData simpleSyncData, String destDataId, Integer newStatus, String remark) {
        int success = syncDataDao.updateStatusAndDestDataIdBySuccess(tenantId, simpleSyncData.getSyncDataId(), destDataId, newStatus, remark);
        syncDataMappingsDao.updateByUniKey(tenantId, simpleSyncData.getSourceObjectApiName(), simpleSyncData.getDestObjectApiName(), simpleSyncData.getSourceDataId(), destDataId, simpleSyncData.getDestDataName(), newStatus, true);
        if (SyncDataStatusEnum.isFailed(newStatus)) {
            ployBreakManager.incrFailedSyncDataNum(tenantId, simpleSyncData.getSyncDataId());
        }

        try {//上报bizlog
            SyncDataErrLog dumpLog = SyncDataErrLog.builder().tenantId(tenantId).appName("erpdss").objectApiName(simpleSyncData.getDestObjectApiName()).build();
            if (SyncDataStatusEnum.isFailed(newStatus)) {
                dumpLog.setErrType(0);
            } else {
                dumpLog.setErrType(-1);
                dumpLog.setSyncErrMsg(SyncDataStatusEnum.getNameByStatus(newStatus));
            }
            if(SandboxUtil.isNotSandbox(eieaConverter, tenantId)) {
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            }
        } catch (Exception e) {
        }
        return success;
    }

    /**
     * 按id顺序返回
     *
     * @param tenantId
     * @param ids
     * @param cachePri 优先从缓存取
     * @return
     */
    public List<SyncDataData> batchGet(String tenantId, List<String> ids, boolean cachePri) {
        if (ids == null || ids.isEmpty()) {
            return Lists.newArrayList();
        }
        List<SyncDataEntity> list;
        if (cachePri) {
            list = syncDataDao.setTenantId(tenantId).listByIdsCachePri(tenantId, ids);
        } else {
            list = syncDataDao.setTenantId(tenantId).listByIds(tenantId, ids);
        }
        list.sort(Comparator.comparing(v -> ids.indexOf(v.getId())));
        return BeanUtil2.deepCopyList(list, SyncDataData.class);
    }
    public Map<String, SyncDataEntity> getAllSyncData(String tenantId) {
        return syncDataDao.setTenantId(tenantId).getTenantSyncDataCache(tenantId);
    }


    public void updateSourceData(String tenantId, String id, ObjectData sourceData,Integer destEventType) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(id) || sourceData == null) {
            log.warn("param error,tenantId:{},id:{},sourceData:{}", tenantId, id, sourceData);
        }
        syncDataDao.setTenantId(tenantId).updateSourceDataById(tenantId, id, sourceData,destEventType);
    }

    public void fillSyncDataMap(SyncDataContextEvent syncDataContextEvent) {
        Map<String, SimpleSyncData> syncDataMap = new HashMap<>();
        syncDataContextEvent.setSyncDataMap(syncDataMap);
        List<String> syncDataIds = new ArrayList<>();
        if (syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap() != null) {
            syncDataIds.addAll(syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap().keySet());
        }
        if (syncDataContextEvent.getSyncDataId() != null) {
            syncDataIds.add(syncDataContextEvent.getSyncDataId());
        }
        if (!syncDataIds.isEmpty()) {
            String tenantId = syncDataContextEvent.getTenantId();
            Integer destTenantType = syncDataContextEvent.getDestTenantType();
            for (String syncDataId : syncDataIds) {
                SyncDataEntity syncData = syncDataDao.getFromThreadLocal(tenantId, syncDataId);
                //写之前补充destDataName,因为有从对象，需要从syncData取DestData
                String destDataName = getDestDataName(tenantId, destTenantType, syncData.getDestData());
                SimpleSyncData simpleSyncData = SimpleSyncData.create()
                        .setSyncDataId(syncDataId)
                        .setSourceObjectApiName(syncData.getSourceObjectApiName())
                        .setDestObjectApiName(syncData.getDestObjectApiName())
                        .setSourceDataId(syncData.getSourceDataId())
                        .setDestDataId(syncData.getDestDataId())
                        .setDestDataName(destDataName);
                syncDataMap.put(syncDataId, simpleSyncData);
            }
        }
    }

    public void fillSimpleSyncData(Map<String, SimpleSyncData> syncDataMap, SyncDataContextEvent completeDataWriteMqData) {
        List<SyncDataContextEvent.WriteResult> writeResults = new ArrayList<>();
        writeResults.add(completeDataWriteMqData.getWriteResult());
        writeResults.addAll(completeDataWriteMqData.getDetailWriteResults());
        for (SyncDataContextEvent.WriteResult writeResult : writeResults) {
            if (writeResult.getSyncDataId() != null) {
                SimpleSyncData simpleSyncData = syncDataMap.get(writeResult.getSyncDataId());
                writeResult.setSimpleSyncData(simpleSyncData);
            }
        }
    }

    /**
     * 从本地缓存取出syncdata，用于异步线程即批量写，传输到下一步线程再处理
     */
    public Map<String, SyncDataEntity> getAndRemoveLocal() {
        Map<String, SyncDataEntity> localMap = syncDataThreadHolder.getAndRemove();
        return localMap;
    }
}
