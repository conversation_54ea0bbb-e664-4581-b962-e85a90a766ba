package com.fxiaoke.open.erpsyncdata.converter.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataMonitorScreen;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ReSyncDataNodeMsgDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ReSyncDataNodeMsg;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailData2;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.EventTriggerService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 重试节点
 */
@Slf4j
@Component
public class ReSyncDataNodeManager {
    @Autowired
    private ReSyncDataNodeMsgDao reSyncDataNodeMsgDao;
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private EventTriggerService eventTriggerService;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;

    /**
     * 保存错误的SyncData到重试集合,删除成功的重试数据
     *
     * @param syncDataContextEvent
     * @param tenantSyncDataCache
     */
    @DataMonitorScreen(tenantId = "#syncDataContextEvent.tenantId", sourceSystemType = "#syncDataContextEvent.sourceTenantType==1?1:2", crmObjApiName = "#syncDataContextEvent.crmObjApiName", outSideObjApiName = "#syncDataContextEvent.outSideObjApiName", dataCenterId = "#syncDataContextEvent.sourceTenantType==1?#syncDataContextEvent.destDataCenterId:#syncDataContextEvent.sourceDataCenterId", ployDetailId = "#syncDataContextEvent.streamId", outDataCount = "#result.data", operationType = CommonConstant.OPERATE_DATA_MAPPING, operateStatus = "2", skipSend = "#result.data>0?false:true")
    public Result<Integer> saveErrorSyncDataByCache(SyncDataContextEvent syncDataContextEvent, List<SyncDataEntity> tenantSyncDataCache) {
        log.info("save error data:{},saveErrorSyncDataByCache:{}", syncDataContextEvent, tenantSyncDataCache);
        return saveErrorSyncData(syncDataContextEvent.getTenantId(), syncDataContextEvent.getSourceTenantType(),
                syncDataContextEvent.getSyncPloyDetailSnapshotId(), syncDataContextEvent.getDataReceiveType(),
                syncDataContextEvent.getDestEventType(), tenantSyncDataCache, true, syncDataContextEvent.getLocale());
    }

    /**
     * @param tenantId
     * @param sourceTenantType
     * @param syncPloyDetailSnapshotId
     * @param dataReceiveType
     * @param masterDestEventType      如果是新增，从数据不保存重试
     * @param tenantSyncDataCache
     * @param needCheckMapping         重试时是否检查中间表存在，true:检查，false:不检查
     * @param locale
     * @return
     */
    public Result<Integer> saveErrorSyncData(String tenantId, Integer sourceTenantType, String syncPloyDetailSnapshotId, Integer dataReceiveType,
                                             Integer masterDestEventType, List<SyncDataEntity> tenantSyncDataCache, Boolean needCheckMapping, String locale) {
        Integer errorDataSize = 0;
        try {
            if (CollectionUtils.isEmpty(tenantSyncDataCache)) {
                return Result.newSuccess();
            }
            SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(tenantId, syncPloyDetailSnapshotId).getData();
            if (syncPloyDetailSnapshotData == null || syncPloyDetailSnapshotData.getSyncPloyDetailData() == null) {
                return Result.newSuccess();
            }
            List<SyncDataEntity> failedSyncDataList = tenantSyncDataCache.stream()
                    .filter(syncDataEntity -> SyncDataStatusEnum.isFailed(syncDataEntity.getStatus())).collect(Collectors.toList());
            errorDataSize = CollectionUtils.isNotEmpty(failedSyncDataList) ? failedSyncDataList.size() : 0;
            SyncPloyDetailData2 syncPloyDetailData = syncPloyDetailSnapshotData.getSyncPloyDetailData();
            if (syncPloyDetailData.getIntegrationStreamNodes() == null || syncPloyDetailData.getIntegrationStreamNodes().getReSyncErrorDataNode() == null
                    || (syncPloyDetailData.getIntegrationStreamNodes().getReSyncErrorDataNode().getReSyncTimeInterval() == null
                    && syncPloyDetailData.getIntegrationStreamNodes().getReSyncErrorDataNode().getReSyncRightNow() == null)
                    || syncPloyDetailData.getIntegrationStreamNodes().getReSyncErrorDataNode().getReSyncTopLimit() == null) {
                return Result.newSuccess(errorDataSize);
            }
            if (CollectionUtils.isNotEmpty(failedSyncDataList)) {//错误数据
                Boolean reSyncRightNow = syncPloyDetailData.getIntegrationStreamNodes().getReSyncErrorDataNode().getReSyncRightNow() == null?false:syncPloyDetailData.getIntegrationStreamNodes().getReSyncErrorDataNode().getReSyncRightNow();
                if (reSyncRightNow || dataReceiveType == null || dataReceiveType != DataReceiveTypeEnum.RE_SYNC_NODE.getType()) {//立即重试或者非自动重试
                    List<ReSyncDataNodeMsg> dataList = Lists.newArrayList();
                    for (SyncDataEntity syncDataEntity : failedSyncDataList) {
                        if (!syncDataEntity.getSourceObjectApiName().equals(syncPloyDetailSnapshotData.getSourceObjectApiName())) {//从数据的syncData
                            //源是crm,不需要重试从对象
                            if (sourceTenantType != null && sourceTenantType == TenantTypeEnum.CRM.getType()) {
                                continue;
                            }
                            //主数据目标事件是新增,不需要重试从对象
                            if (masterDestEventType != null && masterDestEventType == EventTypeEnum.ADD.getType()) {
                                continue;
                            }
                        }
                        ReSyncDataNodeMsg dataNodeMsg = ReSyncDataNodeMsg.createBySyncDataEntity(tenantId, syncPloyDetailSnapshotData, syncDataEntity, needCheckMapping, locale, reSyncRightNow);
                        dataList.add(dataNodeMsg);
                    }
                    if (CollectionUtils.isNotEmpty(dataList)) {
                        reSyncDataNodeMsgDao.batchUpsertDataNodeMsgDoc(tenantId, dataList);
                        if (reSyncRightNow) {
                            List<String> uniqueKeys = dataList.stream().map(ReSyncDataNodeMsg::getUniqueKey).collect(Collectors.toList());
                            List<ReSyncDataNodeMsg> reSyncDataNodeMsgList = reSyncDataNodeMsgDao.getByUniqueKeys(tenantId, uniqueKeys);
                            List<String> deleteUniqueKeys = reSyncDataNodeMsgList.stream().filter(data -> data.getTries() >= data.getTryLimit()).map(data -> data.getUniqueKey()).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(deleteUniqueKeys)) {
                                reSyncDataNodeMsgDao.deleteReSyncDataByUniqueKey(tenantId, deleteUniqueKeys);
                            }
                            reSyncDataNodeMsgList = reSyncDataNodeMsgList.stream().filter(data -> data.getTries() < data.getTryLimit()).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(reSyncDataNodeMsgList)) {
                                List<SyncDataContextEvent> eventDataList = Lists.newArrayList();
                                for (ReSyncDataNodeMsg data : reSyncDataNodeMsgList) {
                                    data.setTries(data.getTries() + 1);
                                    SyncDataContextEvent eventData = new SyncDataContextEvent();
                                    eventData.setSourceData(data.getSyncDataEntity().getSourceData());
                                    eventData.setSourceTenantType(data.getSyncDataEntity().getSourceTenantType());
                                    eventData.setSourceEventType(data.getSyncDataEntity().getSourceEventType());
                                    eventData.setPloyDetailSnapshotId(data.getSyncDataEntity().getSyncPloyDetailSnapshotId());
                                    eventData.setLocale(data.getLocale());
                                    eventData.setDataReceiveType(DataReceiveTypeEnum.RE_SYNC_NODE.getType());
                                    eventDataList.add(eventData);
                                }
                                if (CollectionUtils.isNotEmpty(eventDataList)) {
                                    eventTriggerService.batchSendEventData2DispatcherMqByContext(eventDataList);
                                }
                                reSyncDataNodeMsgDao.batchUpdateDataNodeMsgDoc(tenantId, reSyncDataNodeMsgList);
                            }
                        }
                    }
                }
            }
            List<SyncDataEntity> successSyncDataList = tenantSyncDataCache.stream()
                    .filter(syncDataEntity -> SyncDataStatusEnum.isSuccess(syncDataEntity.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(successSyncDataList)) {
                List<String> uniqueKeys = successSyncDataList.stream().map(syncData -> ReSyncDataNodeMsg.getMsgUniqueKey(tenantId, syncPloyDetailSnapshotData.getSyncPloyDetailId(), syncData.getSourceDataId())).collect(Collectors.toList());
                reSyncDataNodeMsgDao.deleteReSyncDataByUniqueKey(tenantId, uniqueKeys);
            }
        } catch (Exception e) {
            log.info("saveErrorSyncDataByCache Exception={}", e);
        }
        return Result.newSuccess(errorDataSize);
    }

    /**
     * 目前是按企业维度触发重试的，
     *
     * @param tenantId
     */

    public Result<Void> executeReSync(String tenantId) {
        try {
            if (!haveReSyncNodeStream(tenantId).getData()) {
                return Result.newSuccess();
            }
            Long now = System.currentTimeMillis();
            List<ObjectId> needDeleteIdList = Lists.newArrayList();
            Set<String> needDeleteStream = Sets.newHashSet();
            List<ReSyncDataNodeMsg> needUpdateMsgList = Lists.newArrayList();
            List<SyncDataContextEvent> eventDataList = Lists.newArrayList();
            Map<String, SyncPloyDetailEntity> id2Entity = Maps.newHashMap();
            Integer limit = 200;
            while (true) {//要注意死循环
                List<ReSyncDataNodeMsg> dataNodeMsgList = reSyncDataNodeMsgDao.listByTenantIdAndNextReSyncTime(tenantId, now, 0, limit);
                if (CollectionUtils.isEmpty(dataNodeMsgList)) {
                    break;
                }
                for (ReSyncDataNodeMsg reSyncDataNodeMsg : dataNodeMsgList) {
                    String streamId = reSyncDataNodeMsg.getStreamId();
                    if (needDeleteStream.contains(streamId)) {
                        continue;
                    }
                    if (!id2Entity.containsKey(streamId)) {
                        SyncPloyDetailEntity ployDetail = syncPloyDetailManager.getEntryById(tenantId, streamId);
                        if (ployDetail == null) {//已删除
                            needDeleteStream.add(streamId);
                            continue;
                        } else {
                            id2Entity.put(streamId, ployDetail);
                        }
                    }
                    SyncPloyDetailEntity ployDetail = id2Entity.get(streamId);
                    if (ployDetail == null || ployDetail.getIntegrationStreamNodes() == null || ployDetail.getIntegrationStreamNodes().getReSyncErrorDataNode() == null) {//节点已删除
                        needDeleteStream.add(streamId);
                        continue;
                    }
                    if (SyncPloyDetailStatusEnum.ENABLE.getStatus().equals(ployDetail.getStatus())) {//集成流启用，重试
                        if (reSyncDataNodeMsg.getSyncDataEntity() == null || StringUtils.isBlank(reSyncDataNodeMsg.getSourceObjApiName())
                                || StringUtils.isBlank(reSyncDataNodeMsg.getSourceDataId()) || StringUtils.isBlank(reSyncDataNodeMsg.getDestObjApiName())) {//为空，直接删除
                            needDeleteIdList.add(reSyncDataNodeMsg.getId());
                            continue;
                        } else {
                            if (reSyncDataNodeMsg.getNeedCheckMapping() != null && reSyncDataNodeMsg.getNeedCheckMapping()) {
                                SyncDataMappingsEntity sourceMapping = syncDataMappingsDao.setTenantId(tenantId).getByUninKey(tenantId, reSyncDataNodeMsg.getSourceObjApiName(),
                                        reSyncDataNodeMsg.getSourceDataId(), reSyncDataNodeMsg.getDestObjApiName());
                                if (sourceMapping == null) {//为空说明映射删除了，不重试
                                    needDeleteIdList.add(reSyncDataNodeMsg.getId());
                                    continue;
                                }
                            }
                            SyncDataContextEvent eventData = new SyncDataContextEvent();
                            eventData.setSourceData(reSyncDataNodeMsg.getSyncDataEntity().getSourceData());
                            eventData.setSourceTenantType(reSyncDataNodeMsg.getSyncDataEntity().getSourceTenantType());
                            eventData.setSourceEventType(reSyncDataNodeMsg.getSyncDataEntity().getSourceEventType());
                            eventData.setPloyDetailSnapshotId(reSyncDataNodeMsg.getSyncDataEntity().getSyncPloyDetailSnapshotId());
                            eventData.setLocale(reSyncDataNodeMsg.getLocale());
                            eventData.setDataReceiveType(DataReceiveTypeEnum.RE_SYNC_NODE.getType());
                            eventDataList.add(eventData);
                        }
                    }
                    reSyncDataNodeMsg.setTries(reSyncDataNodeMsg.getTries() + 1);
                    reSyncDataNodeMsg.setNextReSyncTime(System.currentTimeMillis() + reSyncDataNodeMsg.getReSyncTimeInterval() * 60 * 1000L);
                    if (reSyncDataNodeMsg.getTries() >= reSyncDataNodeMsg.getTryLimit()) {//达到上限，需要删除,发起重试后就删除，需要在同步的时候识别是自动重试的，不插入数据，否则循环了
                        needDeleteIdList.add(reSyncDataNodeMsg.getId());
                    } else {//更新重试次数，下一次同步时间
                        needUpdateMsgList.add(reSyncDataNodeMsg);
                    }
                }
                if (CollectionUtils.isNotEmpty(eventDataList)) {
                    eventTriggerService.batchSendEventData2DispatcherMqByContext(eventDataList);
                    eventDataList.clear();
                }
                if (CollectionUtils.isNotEmpty(needDeleteStream)) {//集成流已删除，集成流重试节点已删除，删除对应重试数据
                    reSyncDataNodeMsgDao.deleteReSyncDataByStreamId(tenantId, needDeleteStream);
                    needDeleteStream.clear();
                }
                if (CollectionUtils.isNotEmpty(needDeleteIdList)) {//达到次数上限，映射已删除，删除对应重试数据
                    reSyncDataNodeMsgDao.deleteByObjectId(tenantId, needDeleteIdList);
                    needDeleteIdList.clear();
                }
                if (CollectionUtils.isNotEmpty(needUpdateMsgList)) {//更新重试次数，下一次重试时间
                    reSyncDataNodeMsgDao.batchUpdateDataNodeMsgDoc(tenantId, needUpdateMsgList);
                    needUpdateMsgList.clear();
                }
                if (dataNodeMsgList.size() < limit) {
                    break;
                }
            }
        } catch (Exception e) {
            log.info("executeReSync Exception={}", e);
        }
        return Result.newSuccess();
    }

    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 2000)
    private Result<Boolean> haveReSyncNodeStream(String tenantId) {
        List<SyncPloyDetailEntity> streamList = syncPloyDetailManager.listPartialFieldsByTenantId(tenantId);
        for (SyncPloyDetailEntity stream : streamList) {
            if (stream.getIntegrationStreamNodes() != null && stream.getIntegrationStreamNodes().getReSyncErrorDataNode() != null
                    && stream.getIntegrationStreamNodes().getReSyncErrorDataNode().getReSyncTopLimit() != null
                    && stream.getIntegrationStreamNodes().getReSyncErrorDataNode().getReSyncTimeInterval() != null) {
                return Result.newSuccess(true);
            }
        }
        return Result.newSuccess(false);
    }
}
