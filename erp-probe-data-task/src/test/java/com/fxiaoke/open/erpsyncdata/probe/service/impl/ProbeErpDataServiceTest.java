package com.fxiaoke.open.erpsyncdata.probe.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ProbeErpDataService;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
public class ProbeErpDataServiceTest  extends BaseTest {
    @Autowired
    private ProbeErpDataService probeErpDataService;

    @Test
    public void testExecutePloys() {
        Result<Void> result=probeErpDataService.executePloys("88521", Lists.newArrayList());
        System.out.println("");
    }
}
