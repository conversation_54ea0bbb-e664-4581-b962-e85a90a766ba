package com.fxiaoke.open.erpsyncdata.probe.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum;
import com.fxiaoke.open.erpsyncdata.probe.handler.ErpPollingDataHandler;
import com.fxiaoke.open.erpsyncdata.probe.service.BatchTenantJobService;
import com.fxiaoke.open.erpsyncdata.probe.service.CleanSyncDataService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 9:56 2021/3/2
 * @Desc:
 */
@Ignore
public class BatchTenantJobServiceImplTest extends BaseTest {
    @Autowired
    private BatchTenantJobService batchTenantJobService;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private CleanSyncDataService cleanSyncDataService;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private ErpPollingDataHandler erpPollingDataHandler;

    @Test
    public void test() {
        ErpObjectEntity arg = ErpObjectEntity.builder().tenantId("81138").erpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT).build();
        List<ErpObjectEntity> allErpFakeObjects = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("81138")).queryList(arg);
        List<String> allErpObjApiNames=allErpFakeObjects.stream().map(ErpObjectEntity::getErpObjectApiName).collect(Collectors.toList());
        ErpObjectFieldEntity queryArg = ErpObjectFieldEntity.builder().tenantId("81138").fieldDefineType(ErpFieldTypeEnum.master_detail).build();
        List<ErpObjectFieldEntity> erpObjectFieldEntities = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(queryArg.getTenantId())).queryList(queryArg);
        List<String> allErpDetailObjApiNames=erpObjectFieldEntities.stream().map(ErpObjectFieldEntity::getErpObjectApiName).collect(Collectors.toList());
        List<String> allErpMasterObjApiNames = allErpObjApiNames.stream().filter(apiName -> !allErpDetailObjApiNames.contains(apiName)).collect(Collectors.toList());
        System.out.println("");
    }

    @Test
    public void testQuery() {
//        List<SyncDataMappingsEntity> syncDataMappingsEntities =
//                syncDataMappingsDao.setTenantId("80771").listByStatusTimeOutMappings("79675", 1672221814055L,  1610666482714L, "customeraddress_1fde3qap9","object_oGEno__c",0,1000);
//        System.out.println("BatchTenantJobServiceImplTest.testQuery");
    }


    @Test
    public void executeTasks() {
        batchTenantJobService.executeTasks(Lists.newArrayList("79675"));
        System.out.println("");
    }

    @Test
    public void executeTasks2() {
        cleanSyncDataService.executeTasks("79675");
        System.out.println("");
    }

    private final static String NO_SEND_DETAIL_EVENT_CRM_OBJ_SET = "NO_SEND_DETAIL_EVENT_CRM_OBJ_SET";
    @Cached(cacheType = CacheType.LOCAL, expire = 15 * 60)
    public Set<String> getNO_SEND_DETAIL_EVENT_CRM_OBJ_SET() {
        String value = redisDataSource.get(this.getClass().getSimpleName()).get(NO_SEND_DETAIL_EVENT_CRM_OBJ_SET);
        Set<String> set = null;
        if(StringUtils.isNotEmpty(value)) {
            set = JSONObject.parseObject(value,Set.class);
        } else {
            set = configCenterConfig.getNO_SEND_DETAIL_EVENT_CRM_OBJ_SET();
            redisDataSource.get(this.getClass().getSimpleName()).set(NO_SEND_DETAIL_EVENT_CRM_OBJ_SET, JSONObject.toJSONString(set));
            redisDataSource.get(this.getClass().getSimpleName()).expire(NO_SEND_DETAIL_EVENT_CRM_OBJ_SET,15 * 60L);//15分钟过期
        }
        return set;
    }

    @Test
    public void executeJob() {
        erpPollingDataHandler.executeJob(null);
    }
}