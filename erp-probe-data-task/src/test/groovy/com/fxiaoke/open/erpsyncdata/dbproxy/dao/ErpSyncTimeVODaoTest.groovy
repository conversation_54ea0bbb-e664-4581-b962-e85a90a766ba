package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpSyncExtentDTO

import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/24
 */
@Ignore
class ErpSyncTimeVODaoTest {//extends BaseTest {
    @Autowired
    private ErpSyncTimeDao erpSyncTimeDao

    @Test
    void testListAllByTenantId() {
        List<ErpSyncExtentDTO> result = erpSyncTimeDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("79675")).listSyncExtentByTenantId("79675")
        println(result)
    }
}
