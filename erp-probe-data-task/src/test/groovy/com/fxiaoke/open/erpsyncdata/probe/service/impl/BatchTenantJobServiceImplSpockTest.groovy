package com.fxiaoke.open.erpsyncdata.probe.service.impl

import com.facishare.uc.api.event.EnterpriseRunStatusEvent
import com.facishare.uc.api.model.fscore.EnterpriseData
import com.facishare.uc.api.service.EnterpriseEditionService
import com.fxiaoke.open.erpsyncdata.probe.utils.RemoveUnNormalTenantUtil
import com.google.common.collect.Lists
import spock.lang.Specification

class BatchTenantJobServiceImplSpockTest extends Specification {

    //"2个企业是停用的， 原企业列表总数减少2"
    def "case1" () {
        given:
        RemoveUnNormalTenantUtil removeUnNormalTenantUtil = new RemoveUnNormalTenantUtil();
        List<String> tenantIdStrList = Lists.newArrayList("1","2","3","4","5","6","7","8","9","10","11")
        int originalSize = tenantIdStrList.size();
        EnterpriseEditionService enterpriseEditionService = Mock()
        enterpriseEditionService.batchGetEnterpriseData(_) >> [
                new EnterpriseData(enterpriseId:1, runStatus: EnterpriseRunStatusEvent.RUN_STATUS_STOP),
                new EnterpriseData(enterpriseId:2, runStatus: EnterpriseRunStatusEvent.RUN_STATUS_STOP)
                ]

        when:
        removeUnNormalTenantUtil.removeUnNormalTenant(tenantIdStrList, enterpriseEditionService)

        then:
        tenantIdStrList.size() == (originalSize - 2)
    }

    //"0个企业是停用的， 原企业列表总数保持不变"
    def "case2" () {
        given:
        RemoveUnNormalTenantUtil removeUnNormalTenantUtil = new RemoveUnNormalTenantUtil();
        List<String> tenantIdStrList = Lists.newArrayList("1","2","3","4","5","6","7","8","9","10","11")
        int originalSize = tenantIdStrList.size();
        EnterpriseEditionService enterpriseEditionService = Mock()
        enterpriseEditionService.batchGetEnterpriseData(_) >> [

        ]

        when:
        removeUnNormalTenantUtil.removeUnNormalTenant(tenantIdStrList, enterpriseEditionService)

        then:
        tenantIdStrList.size() == originalSize
    }
}