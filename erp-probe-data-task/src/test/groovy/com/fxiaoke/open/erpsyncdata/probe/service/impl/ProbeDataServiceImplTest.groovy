package com.fxiaoke.open.erpsyncdata.probe.service.impl

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpSyncTimeDao
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpSyncTimeManager
import org.bouncycastle.asn1.x509.OtherName
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STLongHexNumber
import spock.lang.Specification


class ProbeDataServiceImplTest extends Specification {

    private ProbeDataServiceImpl probeDataService

    void setup() {
        ErpSyncTimeDao erpSyncTimeDao = Mock()
        ErpSyncTimeManager erpSyncTimeManager = Spy(
                erpSyncTimeDao: erpSyncTimeDao
        )
        probeDataService = new ProbeDataServiceImpl(
                erpSyncTimeManager: erpSyncTimeManager,
                erpSyncTimeDao: erpSyncTimeDao
        )
    }

    def "testCron"() {

        when:
        def res = probeDataService.needSyncWithRule("tenantId", "objApiName", currentTime, updateTime, snapshotCreateTime, pollingIntervalStr, "syncTimeId")
        then:
        res == result
        where:
        result | currentTime   | updateTime    | snapshotCreateTime | pollingIntervalStr
        true   | 1690643160000 | 1690642800000 | 1690642800000      | "{\"dayLimitType\":\"EVERY_DAY\",\"cronExpression\":\"0/6 * * * *\",\"limitValues\":[],\"minutes\":6,\"startDataTime\":\"00:00\",\"endDataTime\":\"23:59\"}"
        false  | 1690643280000 | 1690643220000 | 1690642400000      | "{\"dayLimitType\":\"EVERY_DAY\",\"cronExpression\":\"0/6 * * * *\",\"limitValues\":[],\"minutes\":6,\"startDataTime\":\"00:00\",\"endDataTime\":\"23:59\"}"
        true   | 1690643280000 | 1690642500000 | 1690642400000      | "{\"dayLimitType\":\"EVERY_DAY\",\"cronExpression\":\"0/6 * * * *\",\"limitValues\":[],\"minutes\":6,\"startDataTime\":\"00:00\",\"endDataTime\":\"23:59\"}"
        true   | 1690643280000 | 1690642400000 | 1690642500000      | "{\"dayLimitType\":\"EVERY_DAY\",\"cronExpression\":\"0/6 * * * *\",\"limitValues\":[],\"minutes\":6,\"startDataTime\":\"00:00\",\"endDataTime\":\"23:59\"}"
        false  | 1690643280000 | 1690642400000 | 1690643220000      | "{\"dayLimitType\":\"EVERY_DAY\",\"cronExpression\":\"0/6 * * * *\",\"limitValues\":[],\"minutes\":6,\"startDataTime\":\"00:00\",\"endDataTime\":\"23:59\"}"

    }
}
