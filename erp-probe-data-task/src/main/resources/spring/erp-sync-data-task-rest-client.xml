<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xmlns:p="http://www.springframework.org/schema/p" xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="taskApiHostProfile" class="com.facishare.dubbo.plugin.client.ServerHostProfile">
        <property name="configName" value="erp-sync-data-all"/>
    </bean>

    <bean class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.fxiaoke.open.erpsyncdata.preprocess.service.ErpReSyncDataService"/>
        <property name="serverHostProfile" ref="taskApiHostProfile"/>
    </bean>
    <bean class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.fxiaoke.open.erpsyncdata.preprocess.service.ProbeErpDataService"/>
        <property name="serverHostProfile" ref="taskApiHostProfile"/>
    </bean>
    <bean class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.fxiaoke.open.erpsyncdata.preprocess.service.ErpSyncService"/>
        <property name="serverHostProfile" ref="taskApiHostProfile"/>
    </bean>
    <bean class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.fxiaoke.open.erpsyncdata.preprocess.service.ScanSyncWarnningService"/>
        <property name="serverHostProfile" ref="taskApiHostProfile"/>
    </bean>
    <bean class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService"/>
        <property name="serverHostProfile" ref="taskApiHostProfile"/>
    </bean>
    <bean class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.fxiaoke.open.erpsyncdata.preprocess.service.AllModelDubboService"/>
        <property name="serverHostProfile" ref="taskApiHostProfile"/>
    </bean>

    <bean class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.fxiaoke.open.erpsyncdata.preprocess.service.OnlyRoute2AllGrayService"/>
        <property name="serverHostProfile" ref="taskApiHostProfile"/>
    </bean>

</beans>
