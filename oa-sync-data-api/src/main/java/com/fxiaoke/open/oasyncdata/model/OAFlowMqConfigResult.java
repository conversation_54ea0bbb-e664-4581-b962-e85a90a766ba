package com.fxiaoke.open.oasyncdata.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor

public class OAFlowMqConfigResult {

    private String id;
    /**
     * 事件类型
     */

    private String eventType;
    /**
     * 对象编码：ApprovalTaskObj、BpmTask
     */

    private String objApiName;
    /**
     * APL函数编码
     */

    private String aplApiName;
    /**
     * 企业ID
     */

    private String tenantId;
    /**
     * 创建时间
     */

    private Long createTime;
    /**
     * 更新时间
     */

    private Long updateTime;
    /**
     * 创建人
     */

    private String creator;
    /**
     * 修改人
     */

    private String modifier;

}
