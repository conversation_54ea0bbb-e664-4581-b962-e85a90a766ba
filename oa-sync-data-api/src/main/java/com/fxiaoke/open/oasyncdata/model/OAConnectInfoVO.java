package com.fxiaoke.open.oasyncdata.model;

import com.fxiaoke.open.oasyncdata.arg.CepArg;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * oa企业连接信息
 *
 * <AUTHOR>
 * @date 2021/2/23
 */
@Data
@ApiModel
public class OAConnectInfoVO extends CepArg implements Serializable {
    private static final long serialVersionUID = 3739723344368834569L;


    private String id;

    /**
     * 企业id
     */
    private String tenantId;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 连接参数
     */
    private OAConnectParam connectParams = null;


    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long updateTime;
}