package com.fxiaoke.open.oasyncdata.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 2021/3/18
 * @Desc:
 */
@Data
@ApiModel
public class SyncRuleArg implements Serializable {
    @ApiModelProperty("账号未绑定的时候，是否需要标记为失败，true 则标记，会触发重试。false 则数据状态标记为无需同步")
    private Boolean needSync;



}
