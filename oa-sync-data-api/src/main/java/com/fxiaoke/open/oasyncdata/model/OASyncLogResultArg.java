package com.fxiaoke.open.oasyncdata.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2021/3/18
 * @Desc:
 */
@Data
@ApiModel
public class OASyncLogResultArg implements Serializable {
    @ApiModelProperty("每页数量")
    private Integer pageSize;

    @ApiModelProperty("页码")
    private Integer page;

    @ApiModelProperty("总数")
    private Integer total;

    @ApiModelProperty("数据")
    private List<OASyncLogVO> dataList;
}
