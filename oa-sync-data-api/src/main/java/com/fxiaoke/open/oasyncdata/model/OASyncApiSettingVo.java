package com.fxiaoke.open.oasyncdata.model;

import com.fxiaoke.open.oasyncdata.arg.CepArg;
import com.fxiaoke.open.oasyncdata.constant.ObjectApiEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/1/10 15:36
 * @Version 1.0
 */
@Data
public class OASyncApiSettingVo extends CepArg implements Serializable {

    private static final long serialVersionUID = -5387809623137830022L;
    /**
     * 企业id
     */
    private String tenantId;

    /**
     * 事件类型
     */
    private String eventType = "";
    /**
     * 开启状态（1。开启 2.关闭）
     */
    private String status;


}
