package com.fxiaoke.open.oasyncdata.result;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/6/18 10:31
 * @Version 1.0
 */
@Setter
@Getter
@ToString
public  class InventoryResult implements Serializable {
    private static final long serialVersionUID = 42L;
    @JsonProperty("FID")
    private String FID;
    @JsonProperty("FSTOCKORGID")
    private String FSTOCKORGID;

    @JsonProperty("FSTOCKORGNUMBER")
    private String FSTOCKORGNUMBER;

    @JsonProperty("FSTOCKID")
    private Integer FSTOCKID;

    @JsonProperty("FSTOCKNUMBER")
    private String FSTOCKNUMBER;

    @JsonProperty("FSTOCKNAME")
    private String FSTOCKNAME;

    @JsonProperty("FSTOCKLOCID")
    private String FSTOCKLOCID;

    @JsonProperty("FSTOCKLOC")
    private String FSTOCKLOC;

    @JsonProperty("FLOT")
    private Integer FLOT;

    @JsonProperty("FLOTNUMBER")
    private String FLOTNUMBER;


    @JsonProperty("FMATERIALID")
    private Integer FMATERIALID;

    @JsonProperty("FMATERIALNUMBER")
    private String FMATERIALNUMBER;

    @JsonProperty("FMATERIALNAME")
    private String FMATERIALNAME;


    @JsonProperty("FBASELOCKQTY")
    private Double FBASELOCKQTY;//预留量基本单位

    @JsonProperty("FLOCKQTY")
    private Double FLOCKQTY;//预留量主单位

    @JsonProperty("FSecQty")
    private Double FSecQty;//库存量辅助单位

    @JsonProperty("FSecLockQty")
    private Double FSecLockQty;//预留量辅单位

    @JsonProperty("FSECAVBQTY")
    private Double FSECAVBQTY;//可用量辅助单位

    @JsonProperty("FBASEUNITNAME")
    private String FBASEUNITNAME;//基本单位名称

    @JsonProperty("FBASEUNITNUMBER")
    private String FBASEUNITNUMBER;//基本单位编码

    @JsonProperty("FBASEQTY")
    private Double FBASEQTY;//库存量基本单位

    @JsonProperty("FBASEAVBQTY")
    private Double FBASEAVBQTY;//可用量基本单位

    @JsonProperty("FSTOCKUNITNAME")
    private String FSTOCKUNITNAME;//库存主单位名称

    @JsonProperty("FSTOCKUNITNUMBER")
    private String FSTOCKUNITNUMBER;//库存主单位编码

    @JsonProperty("FQTY")
    private Double FQTY;//库存量主单位

    @JsonProperty("FAVBQTY")
    private Double FAVBQTY;//可用量主单位
}