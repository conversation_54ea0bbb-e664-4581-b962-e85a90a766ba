package com.fxiaoke.open.oasyncdata.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2021/3/18
 * @Desc:
 */
@Data
@ApiModel
public class QueryOASyncLog<PERSON>rg implements Serializable {
    @ApiModelProperty("企业id")
    private String tenantId;
    @ApiModelProperty("数据中心id")
    private String dataCenterId;
    @ApiModelProperty("对象名")
    private String objApiName;

    @ApiModelProperty("同步状态")
    private String status;

    @ApiModelProperty("同步日志ID")
    private String id;

    @ApiModelProperty("数据id")
    private String dataId;

    @ApiModelProperty("处理人Id")
    private String receiverId;

    @ApiModelProperty("事件类型")
    private String eventType;

    @ApiModelProperty("业务类型 待办crmTodo 提醒crmNotify")
    private String businessType;

    @ApiModelProperty("业务对象")
    private String objectName;
    @ApiModelProperty("数据主属性")
    private String dataName;
    @ApiModelProperty("开始时间")
    private Long startTime;
    @ApiModelProperty("结束时间")
    private Long endTime;
    @ApiModelProperty("每页数量")
    private Integer pageSize=100;

    @ApiModelProperty("页码")
    private Integer page=1;
}
