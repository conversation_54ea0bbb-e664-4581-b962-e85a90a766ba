package com.fxiaoke.open.oasyncdata.model;

import com.fxiaoke.open.oasyncdata.constant.OAAPLTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/17
 */
@Data
@ApiModel
public class OAConnectParam implements Serializable {

    private static final long serialVersionUID = -3892271931165465013L;

    /**
     * 获取header的脚本
     */
    @ApiModelProperty(value = "获取header的脚本", example = "return [:]")
    private String headerScript="";

    /**
     * 获取url的脚本
     */
    @ApiModelProperty(value = "获取url的脚本", example = "return [:]")
    private String urlScript="";

    /**
     * 单点登录认证地址
     */
    @ApiModelProperty(value = "单点登录认证地址", example = "return [:]")
    private String ssoAuthUrl;

    /**
     * 结果格式
     */
    @ApiModelProperty("结果格式")
    private ResultFormat resultFormat;

    /**
     * Header信息
     */
    @ApiModelProperty("Header信息")
    private Map<String, String> header;

    /**
     * 授权的信息或者共同的依赖项
     */
    @ApiModelProperty("获取授权的依赖的信息")
    private Map<String,String> commonMap;

    /**
     * APL apiName
     */
    @ApiModelProperty("APL代码的apiName")
    private List<OAAplApiName> aplApiNames;

    /**
     * 对接的OA系统名
     * @param aplType
     * @return
     */
    private String oaTypeName;

    /**
     * 连接器名称
     * @param aplType
     * @return
     */
    private String connectOaName;

    public String getAplApiName(OAAPLTypeEnum aplType) {
        if(CollectionUtils.isEmpty(aplApiNames)) return null;
        for(OAAplApiName aplApiName : aplApiNames) {
            if(StringUtils.equalsIgnoreCase(aplApiName.getType(), aplType.getType())) {
                return aplApiName.getApiName();
            }
        }
        return null;
    }

    public OAAplApiName getOAAplApiName(OAAPLTypeEnum aplType) {
        if(CollectionUtils.isEmpty(aplApiNames)) return null;
        for(OAAplApiName aplApiName : aplApiNames) {
            if(StringUtils.equalsIgnoreCase(aplApiName.getType(), aplType.getType())) {
                return aplApiName;
            }
        }
        return null;
    }


    /**
     * 控制未绑定用户的消息通知
     */
    private Boolean notifyNotBindUse=true;
    /**
     * 接口地址
     */
    @Data
    public static class ServicePath implements Serializable {
        private static final long serialVersionUID = -6091691265483529697L;
        /**
         * 新建路径
         */
        @ApiModelProperty("新建路径")
        private String create;

        /**
         * 处理路径
         */
        @ApiModelProperty("处理路径")
        private String deal;

        /**
         * 删除路径
         */
        @ApiModelProperty("删除路径")
        private String delete;
    }

    /**
     * 结果格式
     */
    @Data
    public static class ResultFormat implements Serializable {
        private static final long serialVersionUID = -8160729428140174044L;
        /**
         * error code 字段
         */
        @ApiModelProperty(value = "error code 字段", example = "errCode")
        private String codeName = "";

        /**
         * error msg 字段
         */
        @ApiModelProperty(value = "error msg 字段", example = "errMsg")
        private String msgName = "";

        /**
         * 数据字段
         */
        @ApiModelProperty(value = "数据字段", example = "data")
        private String dataName = "";

        /**
         * 成功的code
         */
        @ApiModelProperty(value = "成功的code", example = "s106240000")
        private String successCode = "";

        /**
         * webService取json字段
         */
        @ApiModelProperty(value = "webService取json字段", example = "out")
        private String xmlJsonField = "";
    }

    @Data
    public static class OADataCenterInfo implements Serializable{
        private static final long serialVersionUID = -2102380947605903418L;
        private String connectOaName;
        private String id;

    }
}
