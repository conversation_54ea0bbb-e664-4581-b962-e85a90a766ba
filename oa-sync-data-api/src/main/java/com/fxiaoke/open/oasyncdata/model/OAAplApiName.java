package com.fxiaoke.open.oasyncdata.model;

import com.fxiaoke.open.oasyncdata.arg.CepArg;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * APL代码ApiName
 */
@Data
@ApiModel
public class OAAplApiName extends CepArg {
    @ApiModelProperty("函数名称")
    private String apiName;
    @ApiModelProperty("函数类型：可传值为:header,urlScript,dataDetail,common,todo,callback")
    /**
     *  oaRequest类型         现在为了统一，新建一个请求体的函数，包括请求头，请求url,请求体的构造
     */
    private String type;
}
