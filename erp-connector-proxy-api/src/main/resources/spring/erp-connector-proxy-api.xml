<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="serviceConfigManager" class="com.facishare.open.erp.connertor.rest.config.ServiceConfigManager"
          c:configName="erp-connector-proxy-rest"/>

    <bean id="facebookService" class="com.facishare.open.erp.connertor.rest.factory.ConnectorProxyFactoryBean"
          c:type="com.facishare.open.erp.connertor.facebook.FacebookService"
          p:okHttpSupportConfig="erp-sync-data-all"
          p:grayManager-ref="serviceConfigManager"/>

    <bean id="linkedinService" class="com.facishare.open.erp.connertor.rest.factory.ConnectorProxyFactoryBean"
          c:type="com.facishare.open.erp.connertor.linkedin.LinkedinService"
          p:okHttpSupportConfig="erp-sync-data-all"
          p:grayManager-ref="serviceConfigManager"/>

    <bean id="connectorService" class="com.facishare.open.erp.connertor.rest.factory.ConnectorProxyFactoryBean"
          c:type="com.facishare.open.erp.connertor.service.ConnectorService"
          p:okHttpSupportConfig="erp-sync-data-all"
          p:grayManager-ref="serviceConfigManager"/>

    <bean id="oauthLoginService" class="com.facishare.open.erp.connertor.rest.factory.ConnectorProxyFactoryBean"
          c:type="com.facishare.open.erp.connertor.service.OauthLoginService"
          p:okHttpSupportConfig="erp-sync-data-all"
          p:grayManager-ref="serviceConfigManager"/>
</beans>