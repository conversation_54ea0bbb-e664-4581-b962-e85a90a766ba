package com.facishare.open.erp.connertor.rest.factory;

import com.facishare.open.erp.connertor.rest.annotation.RestResource;
import com.facishare.open.erp.connertor.rest.codec.DefaultRestCodec;
import com.facishare.open.erp.connertor.rest.codec.RestCodeC;
import com.facishare.open.erp.connertor.rest.config.ServiceConfig;
import com.facishare.open.erp.connertor.rest.config.ServiceConfigManager;
import com.facishare.open.erp.connertor.rest.utils.RestAnnotationUtils;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.handler.TimeoutSettings;
import com.fxiaoke.common.http.spring.HttpSupportFactoryBean;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.common.reflect.Reflection;
import lombok.Setter;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.web.bind.annotation.RequestMapping;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * <AUTHOR>
 * @date 2023/3/21 10:39:50
 */
public class ConnectorProxyFactoryBean<T> implements FactoryBean<T>, InitializingBean {
    @Setter
    private OkHttpSupport okHttpSupport;
    @Setter
    private ServiceConfigManager grayManager;

    private Class<T> type;

    private RestCodeC codec;

    private String serviceKey;

    public ConnectorProxyFactoryBean(final Class<T> type) {
        this.type = type;
    }

    public void setOkHttpSupportConfig(String config) {
        HttpSupportFactoryBean factoryBean = new HttpSupportFactoryBean();
        factoryBean.setConfigName(config);
        factoryBean.init();
        this.okHttpSupport = factoryBean.getObject();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (Objects.isNull(okHttpSupport)) {
            setOkHttpSupportConfig("fs-rest-proxy-okhttp-config");
        }

        final RestResource annotation = type.getAnnotation(RestResource.class);
        final Class<? extends RestCodeC> codecName = annotation.codec();
        codec = Objects.isNull(codecName) ? DefaultRestCodec.fastJson : codecName.newInstance();

        serviceKey = annotation.value();
    }

    @Override
    public T getObject() throws Exception {
        return Reflection.newProxy(type, (proxy, method, args) -> {
            if (method.getDeclaringClass() == Object.class) {
                return method.invoke(this, args);
            }

            final RequestMapping methodAnnotation = method.getAnnotation(RequestMapping.class);
            final Parameter[] parameters = method.getParameters();
            Object body = RestAnnotationUtils.getBody(args, parameters);
            final String tenantId = RestAnnotationUtils.getTenantId(body);

            final Map<String, String> headers = RestAnnotationUtils.getHeaders(tenantId, args, parameters);

            String keys[] = RestAnnotationUtils.getGrayKeys(tenantId, method, parameters, args);
            final ServiceConfig serviceConfig = grayManager.getServiceConfig(serviceKey, keys);
            final String url = RestAnnotationUtils.getUrl(methodAnnotation.value()[0], args, parameters, serviceConfig);

            final byte[] bytes = codec.encodeArg(body);

            initTraceContext(method, url, bytes);

            return rest(method, headers, serviceConfig, url, bytes);
        });
    }

    private Object rest(final Method method, final Map<String, String> headers, final ServiceConfig serviceConfig, final String url, final byte[] bytes) {
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .headers(Headers.of(headers))
                .tag(TimeoutSettings.class, TimeoutSettings.builder().read(serviceConfig.getSocketTimeOut() / 1000 != 0 ? serviceConfig.getSocketTimeOut() / 1000 : 2).connect(serviceConfig.getConnectionTimeOut() / 1000 != 0 ? serviceConfig.getConnectionTimeOut() / 1000 : 2).build())
                .post(RequestBody.create(MediaType.parse("application/json; charset=utf-8"), bytes));
        Request request = requestBuilder.build();
        return okHttpSupport.syncExecute(request, new SyncCallback() {
            @Override
            public Object response(Response response) throws Exception {
                return handleResponse(response, method.getReturnType());
            }
        });
    }

    public <V> V handleResponse(Response response, Class<V> clazz) throws Exception {
        int statusCode = response.code();
        Map<String, List<String>> headers = Maps.newHashMap();
        Headers tempHeaders = response.headers();
        for (String name : tempHeaders.names()) {
            List<String> values = tempHeaders.values(name);
            headers.put(name.toLowerCase(), values);
        }
        ResponseBody in = response.body();
        return codec.decodeResult(statusCode, headers, in != null ? in.bytes() : null, clazz);
    }

    private TraceContext initTraceContext(final Method method, final String url, final byte[] body) {
        TraceContext context = TraceContext.get();
        context.reset();
        context.inc();
        context.setIface(type);
        context.setMethod(method.getName());
        context.setServerName(grayManager.getConfigName());
        context.setUrl(url);
        context.setParameter(StringUtils.abbreviate(new String(body, UTF_8), 512));
        String traceId = context.getTraceId();
        if (Strings.isNullOrEmpty(traceId) || traceId.equals("null")) {
            if (ConfigHelper.getProcessInfo().getName() != null) {
                traceId = ConfigHelper.getProcessInfo().getName() + "_" + UUID.randomUUID().toString();
            } else {
                traceId = UUID.randomUUID().toString();
            }
            context.setTraceId(traceId);
        }
        return context;
    }

    @Override
    public Class<?> getObjectType() {
        return type;
    }

    @Override
    public boolean isSingleton() {
        return true;
    }
}
