package com.facishare.open.erp.connertor.exception;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/30 11:36:20
 */
@Data
public class OverseasProxyException extends RuntimeException {
    private int errorCode;
    private String errorMsg;

    public OverseasProxyException(final int errorCode, final String errorMsg) {
        super("errorCode:" + errorCode + " errorMessage:" + errorMsg);
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }
}
