package com.facishare.open.erp.connertor.rest.utils;

import com.facishare.open.erp.connertor.rest.annotation.GrayKeys;
import com.facishare.open.erp.connertor.sdk.annotation.TenantId;
import com.facishare.open.erp.connertor.rest.config.ServiceConfig;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.net.HttpHeaders;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @date 2023/3/21 20:30:42
 */
public interface RestAnnotationUtils {
    static Object getBody(final Object[] args, final Parameter[] parameters) {
        return IntStream.range(0, parameters.length)
                .filter(i -> parameters[i].isAnnotationPresent(RequestBody.class))
                .mapToObj(i -> args[i])
                .findFirst()
                .orElse(null);
    }

    static Map<String, String> getHeaders(final String tenantId, Object[] args, Parameter[] parameters) {
        final Map<String, String> headers = initHeaders(tenantId);

        final Map<String, String> parameterHeaders = IntStream.range(0, parameters.length)
                .filter(i -> parameters[i].isAnnotationPresent(RequestHeader.class))
                .mapToObj(i -> i)
                .collect(Collectors.toMap(i -> {
                    final RequestHeader annotation = parameters[i].getAnnotation(RequestHeader.class);
                    return StringUtils.isEmpty(annotation.value()) ? parameters[i].getName() : annotation.value();
                }, i -> (String) args[i]));

        headers.putAll(parameterHeaders);

        return headers.entrySet().stream()
                .filter(entry -> StringUtils.isNotBlank(entry.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    static String[] getGrayKeys(final String tenantId, Method method, Parameter[] parameters, Object[] args) {
        final String name = method.getName();
        final GrayKeys annotation = method.getAnnotation(GrayKeys.class);
        if (Objects.nonNull(annotation) && Objects.nonNull(annotation.value()) && annotation.value().length > 0) {
            return getGrayKeysByMethod(tenantId, parameters, args, name, annotation);
        }

        return getGrayKeysByParameter(tenantId, parameters, args, name);
    }

    static String[] getGrayKeysByParameter(String tenantId, Parameter[] parameters, Object[] args, String name) {
        List<String> keys = new ArrayList<>((parameters.length + 2) * 2);
        keys.add(tenantId);
        keys.add(name);
        IntStream.range(0, parameters.length).boxed()
                .filter(i -> parameters[i].isAnnotationPresent(GrayKeys.class))
                .forEach(i -> keys.add((String) args[i]));
        return keys.toArray(new String[0]);
    }

    static String[] getGrayKeysByMethod(final String tenantId, final Parameter[] parameters, final Object[] args, final String name, final GrayKeys annotation) {
        String[] s = new String[annotation.value().length + 2];
        s[0] = tenantId;
        s[1] = name;
        final Map<String, Object> argMap = IntStream.range(0, parameters.length).boxed()
                .collect(Collectors.toMap(
                        i -> parameters[i].getName(),
                        i -> args[i]
                ));
        for (int i = 0; i < annotation.value().length; i++) {
            s[i + 2] = (String) argMap.get(annotation.value()[i]);
        }

        return s;
    }

    static String getTenantId(final Object body) {
        TraceContext context = TraceContext.get();
        if (Objects.nonNull(context.getEi())) {
            return context.getEi();
        }

        final String tenantId = Arrays.stream(body.getClass().getFields())
                .filter(field -> field.isAnnotationPresent(TenantId.class))
                .findFirst()
                .map(field -> {
                    try {
                        field.setAccessible(true);
                        return (String) field.get(body);
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                })
                .orElse(TraceContext.get().getEi());
        return tenantId;
    }

    static String getUrl(String path, final Object[] args, Parameter[] parameters, final ServiceConfig serviceConfig) {
        // 开头不要有'/'
        path = path.startsWith("/") ? path.substring(1) : path;
        final Map<String, String> pathParams = IntStream.range(0, parameters.length)
                .filter(i -> parameters[i].isAnnotationPresent(PathVariable.class))
                .mapToObj(i -> i)
                .collect(Collectors.toMap(i -> {
                    final PathVariable annotation = parameters[i].getAnnotation(PathVariable.class);
                    return StringUtils.isEmpty(annotation.value()) ? parameters[i].getName() : annotation.value();
                }, i -> (String) args[i]));

        return serviceConfig.getAddress() + RestAnnotationUtils.fillUri(path, pathParams);
    }

    Pattern p = Pattern.compile("(\\{[^{}]+\\})+");

    static String fillUri(String uri, Map<String, String> pathParams) {
        Matcher m = p.matcher(uri);
        String serviceUrl = uri.replace(" ", "");
        while (m.find()) {
            String pathPlaceHold = m.group();
            String pathPlaceHoldWithOutChar = pathPlaceHold.substring(1, pathPlaceHold.length() - 1);
            String value = pathParams.get(pathPlaceHoldWithOutChar);
            if (Strings.isNullOrEmpty(value)) {
                throw new RuntimeException("ConnectorProxyFactoryBean path:" + uri + " Arg " + pathPlaceHoldWithOutChar + " not exist in the parameter list");
            }
            serviceUrl = serviceUrl.replace(pathPlaceHold, value);
        }
        return serviceUrl;
    }

    String X_FS_ENTERPRISE_ID = "X-fs-Enterprise-Id";
    String X_FS_EI = "x-fs-ei";
    String X_TENANT_ID = "x-tenant-id";
    String X_FS_LOCALE = "x-fs-locale";
    String X_RPC_ID = "rpcId";
    String X_FS_RPC_ID = "X-fs-RPC-Id";
    String PEER_NAME = "x-peer-name";
    String X_FS_PEER_NAME = "x-fs-peer-name";
    String PEER_HOST = "x-peer-ip";

    static Map<String, String> initHeaders(String tenantId) {
        TraceContext context = TraceContext.get();

        final HashMap<String, String> headers = new HashMap<>();
        if (Objects.nonNull(tenantId)) {
            headers.put(X_FS_ENTERPRISE_ID, tenantId);
            headers.put(X_TENANT_ID, tenantId);
            headers.put(X_FS_EI, tenantId);
        }
        /**
         * 添加 trace
         */
        if (context.isColor()) {
            headers.put("X-fs-Trace-Color", "1");
        }
        headers.put("X-fs-Trace-Id", context.getTraceId());
        /**
         * 添加国际化
         */
        String locale = context.getLocale();
        if (StringUtils.isBlank(locale)) {
            context.setLocale(Locale.CHINA.toLanguageTag());
        }
        headers.put(HttpHeaders.ACCEPT_LANGUAGE, context.getLocale());
        headers.put(X_FS_LOCALE, context.getLocale());
        headers.put(X_RPC_ID, context.getRpcId());
        headers.put(X_FS_RPC_ID, context.getRpcId());

        /**
         * 添加peer-name peer-host X-FORWARDED-FOR
         */
        headers.put(PEER_HOST, ConfigHelper.getProcessInfo().getIp());
        headers.put(PEER_NAME, ConfigHelper.getProcessInfo().getName());
        headers.put(X_FS_PEER_NAME, ConfigHelper.getProcessInfo().getName());
        return headers;
    }


}
