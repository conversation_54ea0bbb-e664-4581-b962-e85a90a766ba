package com.facishare.open.erp.connertor.codec;

import com.alibaba.fastjson.JSON;
import com.facishare.open.erp.connertor.exception.OverseasProxyException;
import com.facishare.open.erp.connertor.rest.codec.RestCodeC;
import com.facishare.open.erp.connertor.sdk.model.Base;
import com.facishare.open.erp.connertor.util.AesUtil2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Date: 2022/3/2 14:41:35
 */
@Slf4j
public class AesCodec implements RestCodeC {

    @Override
    public <T> byte[] encodeArg(T obj) throws Exception {
        String data = obj instanceof String ? (String) obj : JSON.toJSONString(obj);

        // 加密数据
        return AesUtil2.encrypt(data).getBytes(StandardCharsets.UTF_8);
    }

    @Override
    public <T> T decodeResult(int statusCode, Map<String, List<String>> headers, byte[] bytes, Class<T> clazz) throws Exception {
        final String body = AesUtil2.decrypt(new String(bytes, StandardCharsets.UTF_8));
        if (statusCode >= HttpStatus.MULTIPLE_CHOICES.value()) {
            throw new RuntimeException("response status code:" + statusCode + ", body:" + body);
        }
        T result = JSON.parseObject(body, clazz);
        validateResult(result);
        return result;
    }

    /**
     * 判断调用是否存在业务异常。 如果有业务异常，则抛出，在业务调用的地方根据具体情况处理
     */
    public void validateResult(Object ret) {
        if (ret instanceof Base.Result) {
            Base.Result result = (Base.Result) ret;
            int errorCode = result.getErrCode();
            if (errorCode != 0 || StringUtils.isNotBlank(result.getErrMessage())) {
                throw new OverseasProxyException( errorCode, result.getErrMessage());
            }
        }
    }
}
