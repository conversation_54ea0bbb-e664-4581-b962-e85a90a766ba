package com.fxiaoke.open.erpsyncdata.monitor.manager;

import cn.hutool.core.lang.Dict;
import com.fxiaoke.open.erpsyncdata.monitor.constant.DataSourceType;
import com.fxiaoke.open.erpsyncdata.monitor.dao.CompareDao;
import com.fxiaoke.open.erpsyncdata.monitor.model.CrmDataSourceInfo;
import com.fxiaoke.open.erpsyncdata.monitor.model.ProxyDataSourceInfo;
import com.fxiaoke.open.erpsyncdata.monitor.model.TempDataSourceInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/1/3
 */
@Component
public class DataSourceInfoManager {
    @Autowired
    private CompareDao compareDao;

    public ProxyDataSourceInfo getProxyConnectInfo(String tenantId, String objApiName) {
        Dict dataSourceInfo = compareDao.getDataSourceInfo(tenantId, objApiName, DataSourceType.PROXY);
        ProxyDataSourceInfo proxyDataSourceInfo = dataSourceInfo.toBean(ProxyDataSourceInfo.class);
        return proxyDataSourceInfo;
    }

    public CrmDataSourceInfo getCrmConnectInfo(String tenantId, String objApiName) {
        Dict dataSourceInfo = compareDao.getDataSourceInfo(tenantId, objApiName, DataSourceType.FS_CRM);
        CrmDataSourceInfo connectInfo = dataSourceInfo.toBean(CrmDataSourceInfo.class);
        return connectInfo;
    }

    public TempDataSourceInfo getTempConnectInfo(String tenantId, String objApiName) {
        Dict dataSourceInfo = compareDao.getDataSourceInfo(tenantId, objApiName, DataSourceType.TEMP);
        TempDataSourceInfo connectInfo = dataSourceInfo.toBean(TempDataSourceInfo.class);
        return connectInfo;
    }
}
