package com.fxiaoke.open.erpsyncdata.monitor.helper;

import com.fxiaoke.open.erpsyncdata.monitor.constant.DataSourceType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/1/13
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
public @interface DataSupport {
    DataSourceType value();
}
