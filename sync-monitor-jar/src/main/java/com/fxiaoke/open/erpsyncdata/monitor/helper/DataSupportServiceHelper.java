package com.fxiaoke.open.erpsyncdata.monitor.helper;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.util.ReflectUtil;
import com.fxiaoke.open.erpsyncdata.monitor.constant.DataSourceType;
import com.fxiaoke.open.erpsyncdata.monitor.service.DataSupportService;
import org.springframework.beans.factory.BeanInitializationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/30
 */
@Service
public class DataSupportServiceHelper {
    private static final Map<DataSourceType, DataSupportService> dataSupportServiceMap = new HashMap<>();

    @Autowired
    public void setDataSupportServiceMap(List<DataSupportService> dataSupportServices) {
        for (DataSupportService dataSupportService : dataSupportServices) {
            Type genericSuperclass = dataSupportService.getClass().getGenericSuperclass();
            DataSupport annotation = AnnotationUtil.getAnnotation((Class<?>) genericSuperclass, DataSupport.class);
            if (annotation == null) {
                throw new BeanInitializationException(String.format("DataSupportService %s should add DataSupport annotation", genericSuperclass.getTypeName()));
            }
            DataSupportService put = dataSupportServiceMap.put(annotation.value(), dataSupportService);
            if (put != null) {
                String oldTypeName = put.getClass().getGenericSuperclass().getTypeName();
                if (!oldTypeName.equals(genericSuperclass.getTypeName())) {
                    throw new BeanInitializationException(String.format("%s and %s has same DataSupport annotation %s",
                            oldTypeName, genericSuperclass.getTypeName(), annotation.value()));
                }
            }
        }
    }

    public static DataSupportService get(DataSourceType dataSourceType) {
        return dataSupportServiceMap.get(dataSourceType);
    }
}
