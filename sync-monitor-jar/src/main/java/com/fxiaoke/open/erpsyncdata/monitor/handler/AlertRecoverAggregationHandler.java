package com.fxiaoke.open.erpsyncdata.monitor.handler;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.AlertAggregationType;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataIntegrationNotificationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.SyncDataFailedModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.AlertAggregationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.SyncDataFailedDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.AlertAggregationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncDataFailedEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.monitor.constant.AlertType;
import com.fxiaoke.open.erpsyncdata.monitor.manager.AlertAndBreakManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.AlertNoticeConfig;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 告警或聚合定期检查任务，如果存在告警或恢复数据，发送告警或恢复聚合通知
 * <AUTHOR>
 * @date 2023.07.30
 */
@Slf4j
@Component
@JobHander(value = "alertRecoverAggregationHandler")
public class AlertRecoverAggregationHandler extends IJobHandler {
    @Autowired
    private AlertAggregationDao alertAggregationDao;
    @Autowired
    private SyncDataFailedDao syncDataFailedDao;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    AlertAndBreakManager alertAndBreakManager;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private DataIntegrationNotificationManager dataIntegrationNotificationManager;

    /**
     * 执行任务
     *
     * @param triggerParam
     */
    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        executeJob(triggerParam);
        return ReturnT.SUCCESS;
    }

    public void executeJob(TriggerParam triggerParam) throws Exception {
        TraceUtil.initTrace(UUID.randomUUID().toString());
        log.info("execute alertRecoverAggregationHandler,triggerParam:{}", triggerParam);

        RLock rLock = redissonClient.getLock(CommonConstant.REDIS_LOCK_ALERT_AGGREGATION_DATA);
        if (rLock.tryLock(2, 60 * 10, TimeUnit.SECONDS)) {
            try {
                int limit = 100;

                //1.轮询ERP告警恢复逻辑，数据中心级聚合
                pollingErpAlertRecover(limit);

                //2.轮询ERP告警聚合逻辑，数据中心级聚合
                pollingErpAlertAggregation(limit);

                //3.同步数据失败告警恢复逻辑，数据中心级聚合
                syncDataFailedAlertRecover();

                //4.同步失败数据告警聚合逻辑，数据中心级聚合
                syncDataFailedAlertAggregation(limit);
            } finally {
                rLock.unlock();
            }
        } else {
            log.info("AlertRecoverAggregationHandler.executeJob,get lock failed");
        }
    }

    public void pollingErpAlertRecover(int limit) {
        try {
            Map<String,List<AlertAggregationEntity>> tenantDcDataMap = alertAggregationDao.getPollingErpAlertRecoverDataList(limit);
            if(!tenantDcDataMap.isEmpty()) {
                log.info("AlertRecoverAggregationHandler.pollingErpAlertRecover,alert recover,begin");
                //删除轮询ERP恢复数据
                long count = alertAggregationDao.deleteMany(AlertAggregationType.POLLING_ERP_ALERT,true);
                log.info("AlertRecoverAggregationHandler.pollingErpAlertRecover,delete polling erp alert and recover data,count={}",count);
                //发送轮询ERP恢复聚合通知
                alertAndBreakManager.sendPollingErpAlert(tenantDcDataMap, AlertType.RECOVER);
                log.info("AlertRecoverAggregationHandler.pollingErpAlertRecover,alert recover,end");
            }
        } catch (Exception e) {
            log.info("AlertRecoverAggregationHandler.pollingErpAlertRecover,exception={}",e.getMessage());
        }
    }

    public void pollingErpAlertAggregation(int limit) {
        try {
            Map<String,List<AlertAggregationEntity>> tenantDcDataMap = alertAggregationDao.getAlertAggregationDataList(AlertAggregationType.POLLING_ERP_ALERT,
                    ConfigCenter.pollingErpAlertAggregationTime,
                    limit);
            if(!tenantDcDataMap.isEmpty()) {
                log.info("AlertRecoverAggregationHandler.pollingErpAlertAggregation,polling erp alert aggregation,begin");
                //批量更新 lastAlertTime为当前时间
                batchUpdateLastAlertTime(tenantDcDataMap,AlertAggregationType.POLLING_ERP_ALERT);
                //发送轮询ERP告警聚合通知
                alertAndBreakManager.sendPollingErpAlert(tenantDcDataMap, AlertType.AGGREGATE_ALERT);
                log.info("AlertRecoverAggregationHandler.pollingErpAlertAggregation,polling erp alert aggregation,end");
            }
        } catch (Exception e) {
            log.info("AlertRecoverAggregationHandler.pollingErpAlertAggregation,exception={}",e.getMessage());
        }
    }

    /**
     * 同步数据失败告警恢复逻辑，数据中心级聚合
     */
    public void syncDataFailedAlertRecover() {
        try {
            Map<String, SyncDataFailedEntity> tenantDcPloyDetailDataMap = syncDataFailedDao.getTenantDcPloyDetailDataMap();
            if (tenantDcPloyDetailDataMap.isEmpty()) return;
            //全部同步成功的集成流数据列表
            List<SyncDataFailedModel> syncSuccessDataList = new ArrayList<>();

            tenantDcPloyDetailDataMap.values().stream().forEach((entity -> {
                SyncPloyDetailEntity syncPloyDetailEntity = syncPloyDetailManager.getEntryById(entity.getTenantId(),
                        entity.getPloyDetailId());
                if (syncPloyDetailEntity == null) return;

                //count mapping表比算mongo同步失败条数，在大数据量的情况下，性能更优
                int failedCount = alertAndBreakManager.statSyncFailed(entity.getTenantId(),
                        entity.getDataCenterId(),
                        entity.getPloyDetailId(),
                        syncPloyDetailEntity.getSourceObjectApiName(),
                        syncPloyDetailEntity.getDestObjectApiName());

                if (failedCount == 0) {
                    //当前集成流同步失败的数据已经全部正常同步，从mongo删除同步失败的数据
                    long deleteMany = syncDataFailedDao.deleteMany(entity.getTenantId(),
                            entity.getDataCenterId(),
                            entity.getPloyDetailId());
                    log.info("AlertRecoverAggregationHandler.syncDataFailedAlertRecover,deleteMany={}", deleteMany);

                    //告警恢复后，更新告警状态为已恢复
                    dataIntegrationNotificationManager.updateAlarmStatus(entity.getTenantId(), entity.getPloyDetailId(), AlarmType.SYNC_EXCEPTION,true);

                    AlertNoticeConfig alertNoticeConfig = tenantConfigurationManager.getAlertNoticeConfig(entity.getTenantId(), entity.getDataCenterId());
                    Integer alertThresholds = alertNoticeConfig.getFailedIncrementAlertThresholds();

                    AlertAggregationEntity alertAggregationEntity = alertAggregationDao.getData(entity.getTenantId(),
                            entity.getDataCenterId(),
                            entity.getPloyDetailId(),
                            AlertAggregationType.SYNC_DATA_FAILED_ALERT);

                    log.info("AlertRecoverAggregationHandler.syncDataFailedAlertRecover,alertAggregationEntity={}", alertAggregationEntity);

                    if(alertAggregationEntity==null || alertAggregationEntity.getCount()==0) return;

                    SyncDataFailedModel syncDataFailedModel = new SyncDataFailedModel();
                    syncDataFailedModel.setTenantId(entity.getTenantId());
                    syncDataFailedModel.setDataCenterId(entity.getDataCenterId());
                    syncDataFailedModel.setPloyDetailId(entity.getPloyDetailId());
                    syncDataFailedModel.setCount(alertThresholds * alertAggregationEntity.getCount());
                    syncSuccessDataList.add(syncDataFailedModel);

                    long count = alertAggregationDao.delete(entity.getTenantId(),
                            entity.getDataCenterId(),
                            entity.getPloyDetailId(),
                            AlertAggregationType.SYNC_DATA_FAILED_ALERT);
                    log.info("AlertRecoverAggregationHandler.syncDataFailedAlertRecover,delete alert aggregation data,count={}", count);
                }
            }));

            if (CollectionUtils.isEmpty(syncSuccessDataList)) {
                log.info("AlertRecoverAggregationHandler.syncDataFailedAlertRecover,no sync success integrate stream");
                return;
            }
            Map<String, List<SyncDataFailedModel>> tenantDcDataMap2 = getTenantDcDataMap(syncSuccessDataList);

            //发送同步失败数据告警恢复通知，按集成流进行聚合
            alertAndBreakManager.sendSyncDataFailedAlertRecover(tenantDcDataMap2);
            log.info("AlertRecoverAggregationHandler.syncDataFailedAlertRecover,send recover alert success");
        } catch (Exception e) {
            log.info("AlertRecoverAggregationHandler.syncDataFailedAlertRecover,exception={}",e.getMessage());
        }
    }

    /**
     * 同步失败数据告警聚合逻辑，数据中心级聚合
     * @param limit
     */
    public void syncDataFailedAlertAggregation(int limit) {
        try {
            Map<String,List<AlertAggregationEntity>> tenantDcDataMap = alertAggregationDao.getAlertAggregationDataList(AlertAggregationType.SYNC_DATA_FAILED_ALERT,
                    ConfigCenter.syncDataFailedAlertAggregationTime,
                    limit);
            if(!tenantDcDataMap.isEmpty()) {
                log.info("AlertRecoverAggregationHandler.syncDataFailedAlertAggregation,sync data failed alert aggregation,begin");
                //批量更新 lastAlertTime为当前时间
                batchUpdateLastAlertTime(tenantDcDataMap,AlertAggregationType.SYNC_DATA_FAILED_ALERT);
                //发送同步数据失败告警聚合通知
                alertAndBreakManager.sendSyncDataFailedAlertAggregation(tenantDcDataMap);
                log.info("AlertRecoverAggregationHandler.syncDataFailedAlertAggregation,sync data failed alert aggregation,end");
            }
        } catch (Exception e) {
            log.info("AlertRecoverAggregationHandler.syncDataFailedAlertAggregation,exception={}",e.getMessage());
        }
    }

    private Map<String, List<SyncDataFailedModel>> getTenantDcDataMap(List<SyncDataFailedModel> dataList) {
        Map<String, List<SyncDataFailedModel>> dataMap = new HashMap<>();
        for (SyncDataFailedModel dataEntity : dataList) {
            String key = dataEntity.getTenantId() + "-" + dataEntity.getDataCenterId();
            if (!dataMap.containsKey(key)) {
                List<SyncDataFailedModel> list = new ArrayList<>();
                list.add(dataEntity);
                dataMap.put(key, list);
            } else {
                dataMap.get(key).add(dataEntity);
            }
        }

        return dataMap;
    }

    private void batchUpdateLastAlertTime(Map<String,List<AlertAggregationEntity>> tenantDcDataMap,
                                          AlertAggregationType alertAggregationType) {
        for (String key : tenantDcDataMap.keySet()) {
            List<AlertAggregationEntity> dataList = tenantDcDataMap.get(key);
            List<String> ployDetailIdList = dataList.stream()
                    .map(AlertAggregationEntity::getPloyDetailId)
                    .collect(Collectors.toList());

            String tenantId = dataList.get(0).getTenantId();
            String dcId = dataList.get(0).getDataCenterId();
            long lastAlertTime = System.currentTimeMillis();
            //批量更新lastAlertTime为当前时间
            long count = alertAggregationDao.batchUpdateLastAlertTime(tenantId,
                    dcId,
                    ployDetailIdList,
                    alertAggregationType,
                    lastAlertTime);
            log.info("AlertRecoverAggregationHandler.batchUpdateLastAlertTime,batchUpdateLastAlertTime,tenantId={},dcId={},ployDetailIdList={},alertAggregationType={},lastAlertTime={},count={}",
                    tenantId,dcId,ployDetailIdList,alertAggregationType,lastAlertTime,count);
        }
    }
}
