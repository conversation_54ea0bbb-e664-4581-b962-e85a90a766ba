package com.fxiaoke.open.erpsyncdata.monitor.task;

import com.fxiaoke.open.erpsyncdata.monitor.service.DataCompareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/1/14
 */
@Component
public class CompareTaskPrepare implements ApplicationListener<ContextRefreshedEvent> {
    @Autowired
    private DataCompareService dataCompareService;


    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (event.getApplicationContext().getParent() == null) {
            //加载任务
            dataCompareService.refreshTask(null);
        }
    }
}
