package com.fxiaoke.open.erpsyncdata.monitor.mq.processor;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.monitor.manager.AlertAndBreakManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.MonitorType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.BaseResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/1/4
 */
@Service
@Slf4j
public class PollingErpProcessor extends AbstractMonitorMqProcessor<Pair<TimeFilterArg, Result<?>>> {
    @Autowired
    private AlertAndBreakManager alertAndBreakManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    public PollingErpProcessor() {
        super(MonitorType.POLLING_ERP, new TypeReference<Pair<TimeFilterArg, Result<?>>>() {
        });
    }

    @Override
    void process(Pair<TimeFilterArg, Result<?>> pair) {
        TimeFilterArg filterArg = pair.getKey();
        String tenantId = filterArg.getTenantId();
        BaseResult baseResult = pair.getValue();
        log.info("PollingErpProcessor.process,baseResult={}", JSONObject.toJSONString(baseResult));
        if(StringUtils.isNotEmpty(baseResult.getI18nKey())) {
            if(StringUtils.equalsIgnoreCase(baseResult.getErrCode(), ResultCodeEnum.SUCCESS.getErrCode())) {
                if(!StringUtils.equalsIgnoreCase(baseResult.getErrMsg(),ResultCodeEnum.SUCCESS.getErrMsg())) {
                    log.info("PollingErpProcessor.process,success error msg changed");
                } else {
                    baseResult.setErrMsg(i18NStringManager.getByEi2(baseResult.getI18nKey(),tenantId,baseResult.getErrMsg(),baseResult.getI18nExtra()));
                }
            } else {
                baseResult.setErrMsg(i18NStringManager.getByEi2(baseResult.getI18nKey(),tenantId,baseResult.getErrMsg(),baseResult.getI18nExtra()));
            }
        }
        alertAndBreakManager.handelPollingErpResult(filterArg, baseResult);
    }
}
