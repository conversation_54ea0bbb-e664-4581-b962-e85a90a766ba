package com.fxiaoke.open.erpsyncdata.monitor.manager;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.AlertAggregationType;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpAlarmRuleEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncObjectAndTenantMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AlarmRuleMatchModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.SyncDataFailedModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.AlertAggregationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.SyncDataFailedDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.SyncFailedStatDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.AlertAggregationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncDataFailedEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.monitor.constant.AlertType;
import com.fxiaoke.open.erpsyncdata.monitor.model.AlertMessageModel;
import com.fxiaoke.open.erpsyncdata.monitor.task.SyncDataFailedTask;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.NotifyType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.PollingNotFirstErpAlarmData;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.SyncFailedStat;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.BaseResult;
import com.fxiaoke.open.erpsyncdata.preprocess.service.EmailNotificationService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SmsNotificationService;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 告警和熔断
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/11/19
 */
@Component
@Slf4j
public class AlertAndBreakManager {
    @Autowired
    private SyncPloyManager syncPloyManager;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private DataCenterManager dataCenterManager;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private SyncPloyDetailManager adminSyncPloyDetailManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private SyncFailedStatDao syncFailedStatDao;
    @Autowired
    private SyncDataFailedDao syncDataFailedDao;
    @Autowired
    private AlertAggregationDao alertAggregationDao;
    @Autowired
    private DataIntegrationNotificationManager dataIntegrationNotificationManager;
    @Autowired
    private SyncDataFailedTask syncDataFailedTask;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ErpAlarmRuleManager erpAlarmRuleManager;
    @Autowired
    private EmailNotificationService emailNotificationService;
    @Autowired
    private SmsNotificationService smsNotificationService;

    private final Joiner joiner = Joiner.on("-").skipNulls();
    /**
     * 轮询异常超时时间，14天，即14天后即使没达到阈值也会删除计数
     */
    private final long pollingExpireSeconds = TimeUnit.DAYS.toSeconds(14L);
    /**
     * 异常增量预估值超时时间，7天，预计定时任务每天需要刷新
     */
    private final long syncFailedExpireSeconds = TimeUnit.DAYS.toSeconds(7L);
    /**
     * 每6小时重置一下set
     */
    private final long idSetExpireSeconds = TimeUnit.HOURS.toSeconds(6L);

    /**
     * 处理轮询结果。
     * 监测连续失败，和发送告警
     *
     * @param timeFilterArg
     */
    @LogLevel
    public void handelPollingErpResult(TimeFilterArg timeFilterArg, BaseResult pollErpResult) {
        RLock lock = redissonClient.getLock(CommonConstant.REDIS_LOCK_HANDLE_POLLING_ERP_MQ + timeFilterArg.getTenantId());
        try {
            if(lock.tryLock(15, TimeUnit.SECONDS)) {
                try {
                    handelPollingErpResultNoCat(timeFilterArg, pollErpResult);
                } catch (Exception e) {
                    log.error("AlertAndBreakManager.handelPollingErpResult,exception",e);
                } finally {
                    lock.unlock();
                }
            } else {
                log.info("AlertAndBreakManager.handelPollingErpResult,try lock failed");
            }
        } catch (Exception e) {
            log.info("AlertAndBreakManager.handelPollingErpResult,try lock exception,msg={}",e.getMessage());
        }
    }

    /**
     * 处理轮询临时库异常。
     * 直接发送告警到超级管理员
     * 如果出现就需要解决，
     * 暂时每次发生都发送如果想防止消息太多可以考虑增加队列
     *
     * @param timeFilterArg
     * @param result
     */
    public void handelPollingTempFailed(TimeFilterArg timeFilterArg, BaseResult result) {
        try {
            handelPollingTempResultNoCat(timeFilterArg, result);
        } catch (Exception e) {
            log.error("handle temp result failed", e);
        }
    }

    private void handelPollingTempResultNoCat(TimeFilterArg timeFilterArg, BaseResult result) {
        //发送到超级管理员
        String msg = i18NStringManager.getByEi2(I18NStringEnum.s699.getI18nKey(),
                timeFilterArg.getTenantId(),
                String.format(I18NStringEnum.s699.getI18nValue(), DateUtil.now(),"\n", JacksonUtil.toJson(timeFilterArg),"\n", JacksonUtil.toJson(result)),
                Lists.newArrayList(DateUtil.now(),"\n", JacksonUtil.toJson(timeFilterArg),"\n", JacksonUtil.toJson(result)));
        SendAdminNoticeArg sendAdminNoticeArg = SendAdminNoticeArg.builder()
                .tenantId(timeFilterArg.getTenantId())
                .msgTitle(i18NStringManager.getByEi(I18NStringEnum.s698,timeFilterArg.getTenantId()))
                .msg(msg)
                .build().addTraceInfo();
        notificationService.sendSuperAdminNotice(sendAdminNoticeArg);
    }

    /**
     * @param timeFilterArg
     */
    private void handelPollingErpResultNoCat(TimeFilterArg timeFilterArg, BaseResult pollErpResult) {
        log.info("AlertAndBreakManager.handelPollingErpResultNoCat,timeFilterArg={},pollErpResult={}",timeFilterArg,pollErpResult);
        String tenantId = timeFilterArg.getTenantId();
        String dcId = dataCenterManager.getDataCenterByObjApiName(timeFilterArg.getTenantId(), timeFilterArg.getObjAPIName());
        String objAPIName = timeFilterArg.getObjAPIName();
        String snapshotId = timeFilterArg.getSnapshotId();
        if (StringUtils.isEmpty(snapshotId)) {
            //非正常轮询不记录
            return;
        }
        SyncPloyDetailSnapshotEntity snap = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId, snapshotId);
        String ployDetailId = snap.getSyncPloyDetailId();

        Integer operationType = timeFilterArg.getOperationType();
        String name = joiner.join(tenantId, objAPIName, operationType, snapshotId);
        String redisKey = String.format(CommonConstant.REDIS_KEY_POLLING_CONTINUE_FAILED_COUNT, name);
        MergeJedisCmd jedis = redisDataSource.get(this.getClass().getSimpleName());

        //读取告警数据，数据中心级
        AlertAggregationEntity alertAggregationEntity = alertAggregationDao.getData(tenantId,
                dcId,
                null,
                AlertAggregationType.POLLING_ERP_ALERT);

        //读取告警数据，集成流级
        AlertAggregationEntity alertAggregationEntity2 = alertAggregationDao.getData(tenantId,
                dcId,
                ployDetailId,
                AlertAggregationType.POLLING_ERP_ALERT);

        if (pollErpResult.isSuccess()) {
            //删除连续失败次数标识
            Long del = jedis.del(redisKey);
            log.info("del polling erp redis key,del:{}", del);

            //告警恢复后，更新告警状态为已恢复
            dataIntegrationNotificationManager.updateAlarmStatus(tenantId, ployDetailId,AlarmType.POLLING_ERP_API_EXCEPTION,true);

            if(alertAggregationEntity2!=null) {
                //当前集成流轮询ERP已恢复，设置恢复标识，后面定时任务会统一发送聚合后的告警恢复通知
                alertAggregationEntity2.setAlertRecover(true);
                alertAggregationEntity2.setUpdateTime(new Date());
                alertAggregationDao.replace(alertAggregationEntity2);
            }
        } else {
            //轮询失败，增加连续失败次数
            Long incr = redisDataSource.incrAndExpire(redisKey, 1L, pollingExpireSeconds,this.getClass().getSimpleName());
//            AlertNoticeConfig alertNoticeConfig = tenantConfigurationManager.getAlertNoticeConfig(tenantId, dcId);
//            Integer thresholds = alertNoticeConfig.getPollingErpAlertThresholds();

            List<ErpAlarmRuleEntity> pollingErpConfigList = erpAlarmRuleManager.getAlarmTypeConfig(tenantId,
                    dcId,
                    ployDetailId,
                    AlarmType.POLLING_ERP_API_EXCEPTION);
            if(CollectionUtils.isEmpty(pollingErpConfigList)) {
                log.warn("no alarm rule for polling erp api exception");
                return;
            }
            AlarmRuleMatchModel thresholdMathResult = erpAlarmRuleManager.getThresholdMathResult(pollingErpConfigList, incr.intValue());
            Integer thresholds = thresholdMathResult.getMatchThreshold();
            if (thresholdMathResult.getMatchThreshold()!=null) {
                //达到连续失败阈值，重置计数，并发送告警。如果出现并发,统计可能会有误差。
                if(thresholdMathResult.isGreaterThanMaxThreshold()) {
                    Long del = jedis.del(redisKey);
                    log.info("incr arrive thresholds,del:{}", del);
                }

                //自定义告警的优先级最高，不聚合，直接发送告警
                if(thresholdMathResult.getMatchEntity().getAlarmRuleType()==AlarmRuleType.CUSTOM) {
                    log.info("AlertAndBreakManager.handelPollingErpResultNoCat,send polling erp custom alarm directly");
                    sendPollingErpFirstAlert(tenantId,
                            dcId,
                            snapshotId,
                            operationType,
                            thresholds,
                            pollErpResult.getErrMsg(),
                            thresholdMathResult.getMatchEntity().getAlarmLevel());
                    return;
                }

                log.info("AlertAndBreakManager.handelPollingErpResultNoCat,alertAggregationEntity={}",alertAggregationEntity);
                log.info("AlertAndBreakManager.handelPollingErpResultNoCat,alertAggregationEntity2={}",alertAggregationEntity2);
                if(alertAggregationEntity==null) {
                    //首次触发告警，先设置告警标识
                    alertAggregationDao.insert(tenantId,
                            dcId,
                            ployDetailId,
                            AlertAggregationType.POLLING_ERP_ALERT,
                            AlarmLevel.URGENT,
                            System.currentTimeMillis(),
                            1,
                            false,
                            pollErpResult.getErrCode(),
                            pollErpResult.getErrMsg(),
                            pollErpResult.getTraceMsg());
                    //再发送首次告警通知
                    sendPollingErpFirstAlert(tenantId,
                            dcId,
                            snapshotId,
                            operationType,
                            thresholds,
                            pollErpResult.getErrMsg(),
                            thresholdMathResult.getMatchEntity().getAlarmLevel());
                } else {
                    //如果已经触发过告警
                    //执行聚合告警逻辑，告警次数+1
                    if(alertAggregationEntity2!=null) {
                        alertAggregationEntity2.setCount(alertAggregationEntity2.getCount()+1);
                        alertAggregationEntity2.setLastAlertTime(System.currentTimeMillis());
                        alertAggregationEntity2.setUpdateTime(new Date());
                        alertAggregationDao.replace(alertAggregationEntity2);
                    } else {
                        //当前集成流首次触发告警，先设置告警标识
                        alertAggregationDao.insert(tenantId,
                                dcId,
                                ployDetailId,
                                AlertAggregationType.POLLING_ERP_ALERT,
                                AlarmLevel.URGENT,
                                System.currentTimeMillis(),
                                1,
                                false,
                                pollErpResult.getErrCode(),
                                pollErpResult.getErrMsg(),
                                pollErpResult.getTraceMsg());
                    }
                }
            }
        }
    }

    public void handelPollingNotFirstErp(PollingNotFirstErpAlarmData data) {
        String tenantId = data.getTenantId();
        String snapshotId = data.getSnapshotId();
        if (StringUtils.isEmpty(snapshotId)) {
            //非正常轮询不记录
            return;
        }
        SyncPloyDetailSnapshotEntity snap = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId, snapshotId);
        String ployDetailId = snap.getSyncPloyDetailId();
        String dcId=snap.getSyncPloyDetailData().getSourceDataCenterId();
        List<ErpAlarmRuleEntity> pollingErpConfigList = erpAlarmRuleManager.getAlarmTypeConfig(tenantId,
                dcId,
                ployDetailId,
                AlarmType.POLLING_ERP_API_EXCEPTION);
        if (CollectionUtils.isEmpty(pollingErpConfigList)) {
            log.warn("no alarm rule for polling erp api exception");
            return;
        }
        for (ErpAlarmRuleEntity entity : pollingErpConfigList) {
            if (entity.getAlarmSetting() != null && entity.getAlarmSetting().getIsAlarmPollingNotFirstPageError() != null
                    && entity.getAlarmSetting().getIsAlarmPollingNotFirstPageError()) {
                SendTextNoticeArg noticeArg = new SendTextNoticeArg();
                noticeArg.setMsg(data.getMsg());
                noticeArg.setMsgTitle(data.getMsgTitle());
                noticeArg.setTenantId(tenantId);
                noticeArg.setDataCenterId(dcId);
                noticeArg.setPloyDetailId(ployDetailId);
                sendByAlarmRule(tenantId, entity, noticeArg, entity.getAlarmRuleType(), entity.getAlarmRuleName(), entity.getAlarmType(), entity.getAlarmLevel());
            }
        }
    }
    /**
     * 发送轮询ERP异常 首次 告警通知，集成流级，不聚合
     * @param tenantId
     * @param dcId
     * @param snapshotId
     * @param operationType
     * @param exceptionCount
     * @param errMsg
     */
    private void sendPollingErpFirstAlert(String tenantId,
                                          String dcId,
                                          String snapshotId,
                                          Integer operationType,
                                          Integer exceptionCount,
                                          String errMsg,
                                          AlarmLevel alarmLevel) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
        SyncPloyDetailSnapshotEntity snap = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId, snapshotId);
        SyncPloyDetailEntity ployDetail = adminSyncPloyDetailManager.getEntryById(tenantId, snap.getSyncPloyDetailId());
        String streamName = ployDetail.getIntegrationStreamName();
        String pollingTypeName = EventTypeEnum.getNameByEiAndType(i18NStringManager,tenantId,operationType);
        String dcName = connectInfo.getDataCenterName();

        String title =  i18NStringManager.getByEi(I18NStringEnum.s700,tenantId);

        String alertLevelTips = "【" + alarmLevel.getName(i18NStringManager,null, tenantId) + "】";
        String content = alertLevelTips + i18NStringManager.getByEi(I18NStringEnum.s2268,tenantId) + "\n" + i18NStringManager.getByEi2(I18NStringEnum.s701.getI18nKey(),
                tenantId,
                String.format(I18NStringEnum.s701.getI18nValue(), exceptionCount, StrUtil.sub(errMsg, 0, 500),"\n","\n"),
                Lists.newArrayList(exceptionCount+"", StrUtil.sub(errMsg, 0, 500),"\n","\n"));


        String streamInfo = i18NStringManager.getByEi2(I18NStringEnum.s702.getI18nKey(),
                tenantId,
                String.format(I18NStringEnum.s702.getI18nValue(), dcName,"\n", streamName, pollingTypeName),
                Lists.newArrayList(dcName,"\n", streamName, pollingTypeName));

        String tenantInfo = i18NStringManager.getByEi2(I18NStringEnum.s703.getI18nKey(),
                tenantId,
                String.format(I18NStringEnum.s703.getI18nValue(), connectInfo.getEnterpriseName(), tenantId),
                Lists.newArrayList(connectInfo.getEnterpriseName(), tenantId)) + "\n";

        Pair triple = new ImmutablePair(tenantId, dcId);

        AlertMessageModel message = AlertMessageModel.builder().tenantId(tenantId)
                .dataCenterId(dcId)
                .ployDetailId(ployDetail.getId())
                .errorMessage(content)
                .streamName(streamName)
                .streamInfo(streamInfo)
                .tenantInfo(tenantInfo)
                .title(title)
                .build();

        //localDispatcherUtil.produceData(JSONObject.toJSONString(triple), Lists.newArrayList(message));
        alert(message,
                AlarmRuleType.GENERAL,
                AlarmRuleType.GENERAL.getName(i18NStringManager,null,tenantId),
                AlarmType.POLLING_ERP_API_EXCEPTION,
                alarmLevel);
        log.info("send polling erp first alert, tenantId:{}, dcId:{}, alarmLevel:{}, message:{}", tenantId, dcId, alarmLevel, message);
    }

    /**
     * 发送轮询ERP 告警聚合和恢复通知，按数据中心进行聚合
     * @param tenantDcDataMap
     * @param alertType
     */
    public void sendPollingErpAlert(Map<String,List<AlertAggregationEntity>> tenantDcDataMap,AlertType alertType) {
        log.info("send polling erp alert, alertType:{}, tenantDcDataMap:{}", alertType, tenantDcDataMap);
        for(String key : tenantDcDataMap.keySet()) {
            List<AlertAggregationEntity> syncSuccessDataList = tenantDcDataMap.get(key);
            String tenantId = null;
            String dcId = null;
            String tenantInfo = null;
            String dcName = null;
            String streamInfo = null;
            String streamName = null;
            String lastExceptionMsg = null;

            int totalSize = 0;
            for(AlertAggregationEntity entity : syncSuccessDataList) {
                tenantId = entity.getTenantId();
                dcId = entity.getDataCenterId();
                String ployDetailId = entity.getPloyDetailId();

                SyncPloyDetailEntity ployDetail = adminSyncPloyDetailManager.getEntryById(tenantId, ployDetailId);
                streamName = ployDetail.getIntegrationStreamName();
                ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
                dcName = connectInfo.getDataCenterName();
                tenantInfo = i18NStringManager.getByEi2(I18NStringEnum.s703.getI18nKey(),
                        tenantId,
                        String.format(I18NStringEnum.s703.getI18nValue(), connectInfo.getEnterpriseName(), tenantId),
                        Lists.newArrayList(connectInfo.getEnterpriseName(), tenantId)) + "\n";
                if(StringUtils.isEmpty(streamInfo)) {
                    streamInfo = i18NStringManager.getByEi2(I18NStringEnum.s704.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s704.getI18nValue(), dcName,"\n", streamName),
                            Lists.newArrayList(dcName,"\n", streamName));
                } else {
                    streamInfo += "," + streamName;
                }
                lastExceptionMsg = entity.getErrMsg();

                totalSize += entity.getCount();
            }

//            AlertNoticeConfig alertNoticeConfig = tenantConfigurationManager.getAlertNoticeConfig(tenantId, dcId);
//            Integer thresholds = alertNoticeConfig.getPollingErpAlertThresholds();

            ErpAlarmRuleEntity alarmRuleEntity = erpAlarmRuleManager.getAlarmTypeConfig2(tenantId,
                    dcId,
                    AlarmRuleType.GENERAL,
                    AlarmType.POLLING_ERP_API_EXCEPTION);
            Integer thresholds = alarmRuleEntity.getThreshold();

            //按数据中心聚合告警恢复信息
            String content = null;
            switch (alertType) {
                case AGGREGATE_ALERT:
                    content = i18NStringManager.getByEi(I18NStringEnum.s2268,tenantId) + "\n" + i18NStringManager.getByEi2(I18NStringEnum.s701.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s701.getI18nValue(), thresholds * totalSize, StrUtil.sub(lastExceptionMsg, 0, 500),"\n","\n"),
                            Lists.newArrayList((thresholds * totalSize)+"", StrUtil.sub(lastExceptionMsg, 0, 500),"\n","\n"));
                    break;
                case RECOVER:
                    content = i18NStringManager.getByEi(I18NStringEnum.s705,tenantId)+"\n";
                    break;
            }
            AlertMessageModel message = AlertMessageModel.builder()
                    .tenantId(tenantId)
                    .dataCenterId(dcId)
                    .ployDetailId(null)
                    .errorMessage(content)
                    .streamName(streamName)
                    .streamInfo(streamInfo)
                    .tenantInfo(tenantInfo)
                    .title(getAlertTitle(tenantId,alertType))
                    .build();

            alert(message,
                    AlarmRuleType.GENERAL,
                    AlarmRuleType.GENERAL.getName(i18NStringManager,null,tenantId),
                    AlarmType.POLLING_ERP_API_EXCEPTION,
                    AlarmLevel.URGENT);
            log.info("send polling erp alert, tenantId:{}, dcId:{}, message:{}", tenantId, dcId, message);
        }
    }

    private void alert(AlertMessageModel lastMessageModel,
                       AlarmRuleType alarmRuleType,
                       String alarmRuleName,
                       AlarmType alarmType,
                       AlarmLevel alarmLevel) {
        log.info("send alert, model={}",lastMessageModel);
        SendTextNoticeArg noticeArg = new SendTextNoticeArg();
        noticeArg.setMsg(lastMessageModel.getErrorMessage() + lastMessageModel.getStreamInfo());
        noticeArg.setMsgTitle(lastMessageModel.getTitle());
        noticeArg.setTenantId(lastMessageModel.getTenantId());
        noticeArg.setDataCenterId(lastMessageModel.getDataCenterId());
        noticeArg.setPloyDetailId(lastMessageModel.getPloyDetailId());
        //这里不知道为啥没把具体的规则传进来，又查了一遍，还固定死了AlarmRuleType
        ErpAlarmRuleEntity alarmRuleEntity = erpAlarmRuleManager.getAlarmTypeConfig2(lastMessageModel.getTenantId(),
                lastMessageModel.getDataCenterId(),
                AlarmRuleType.GENERAL,
                alarmType);
        //这里不知道为啥不使用ErpAlarmRuleEntity里面的alarmRuleType,alarmRuleName,alarmType,alarmLevel
        sendByAlarmRule(lastMessageModel.getTenantId(),alarmRuleEntity,noticeArg, alarmRuleType, alarmRuleName, alarmType, alarmLevel);

        //发送给纷享相关人员,不再发送到超级管理员
        SendAdminNoticeArg sendAdminNoticeArg = SendAdminNoticeArg.builder()
                .tenantId(lastMessageModel.getTenantId())
                .dcId(lastMessageModel.getDataCenterId())
                .msgTitle(lastMessageModel.getTitle())
                .msg(lastMessageModel.getErrorMessage() + lastMessageModel.getTenantInfo() + lastMessageModel.getStreamInfo())
                .needFillPreDbName(false).build();
        sendAdminNoticeArg.addTraceInfo();
        notificationService.sendCSM(sendAdminNoticeArg, alarmRuleType, alarmRuleName, alarmType, alarmLevel);
        log.info("send alert, sendAdminNoticeArg:{}", sendAdminNoticeArg);
    }

    private void sendByAlarmRule(String tenantId,ErpAlarmRuleEntity alarmRuleEntity,SendTextNoticeArg noticeArg, AlarmRuleType alarmRuleType,
                                 String alarmRuleName, AlarmType alarmType, AlarmLevel alarmLevel) {
        List<Integer> userIdList = alarmRuleEntity.getUserIdList2();
        List<String> roleIdList = alarmRuleEntity.getRoleIdList();
        //发送给企业内的人
        if(CollectionUtils.isNotEmpty(alarmRuleEntity.getNotifyType())){
            if (alarmRuleEntity.getNotifyType().contains(NotifyType.QIXIN.name())) {//企信
                notificationService.sendNoticeByConfig(noticeArg, userIdList, roleIdList, alarmRuleType, alarmRuleName, alarmType, alarmLevel);//企信
            }
            if (alarmRuleEntity.getNotifyType().contains(NotifyType.EMAIL.name())) {//邮件
                String subject=noticeArg.getMsgTitle();
                String content = noticeArg.getMsg();
                emailNotificationService.sendEmailNotice(tenantId,userIdList,roleIdList,subject,content);
            }
            if (alarmRuleEntity.getNotifyType().contains(NotifyType.SMS.name())) {//短信
                String subject=noticeArg.getMsgTitle();
                String content = noticeArg.getMsg();
                content = String.format("%s\n%s", subject ,content);
                smsNotificationService.sendSmsNotice(tenantId,userIdList,roleIdList,content);
            }
        }else{//历史数据
            notificationService.sendNoticeByConfig(noticeArg, userIdList, roleIdList, alarmRuleType, alarmRuleName, alarmType, alarmLevel);//企信
        }
        log.info("send alert, noticeArg:{}", noticeArg);
    }


    @LogLevel(LogLevelEnum.TRACE)
    public void incrFailedSyncDataNum(String tenantId, SyncDataEntity simple) {
        RLock lock = redissonClient.getLock(CommonConstant.REDIS_LOCK_HANDLE_SYNC_FAILED_DATA_MQ + tenantId);
        try {
            if(lock.tryLock(15, TimeUnit.SECONDS)) {
                try {
                    incrFailedSyncDataNumExecute(tenantId, simple);
                } catch (Exception e) {
                    log.error("AlertAndBreakManager.incrFailedSyncDataNum,exception",e);
                } finally {
                    lock.unlock();
                }
            } else {
                log.info("AlertAndBreakManager.incrFailedSyncDataNum,try lock failed");
            }
        } catch (Exception e) {
            log.info("AlertAndBreakManager.incrFailedSyncDataNum,try lock exception,msg={}",e.getMessage());
        }
    }


    private void incrFailedSyncDataNumExecute(String tenantId, SyncDataEntity simple) {
        log.info("incrFailedSyncDataNumExecute,tenantId={},simple={}",tenantId,simple);
        //每个集成流记录一个，redis记录的是预估总增量（上次熔断后）
        String snapId = simple.getSyncPloyDetailSnapshotId();
        SyncPloyDetailSnapshotEntity snapshot = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId, snapId);
        if (!snapshot.getSourceObjectApiName().equals(simple.getSourceObjectApiName())) {
            //不是主对象
            return;
        }
        String ployDetailId = snapshot.getSyncPloyDetailId();
        String idSetTopic = getIdSetTopic(tenantId, ployDetailId);
        String srcDataId = simple.getSourceDataId();
        //检查是否最近同步的数据
        MergeJedisCmd jedisCmd = redisDataSource.get(this.getClass().getSimpleName());
        String dcId = dataCenterManager.getDataCenterBySnapshotId(tenantId, snapId);

        //检查srcDataId在库里是否存在
        SyncDataFailedEntity data = syncDataFailedDao.getData(tenantId, dcId, ployDetailId, srcDataId);
        log.info("incrFailedSyncDataNumExecute,data={}",data);
        if(data!=null || syncDataFailedTask.exist(tenantId,dcId,ployDetailId,srcDataId)) {
            return;
        }

        //如果不存在，把srcDataId插入到库里
        //syncFailedDataDao.replace(tenantId,dcId,ployDetailId,srcDataId);
        //把数据插入到本地缓存里面，定时任务会把数据批量写入Mongo
        syncDataFailedTask.insert(tenantId,dcId,ployDetailId,srcDataId);
//        PloyBreakNoticeConfig ployBreakNoticeConfig = tenantConfigurationManager.getPloyBreakNoticeConfig(tenantId, dcId);
//        Integer breakThresholds = ployBreakNoticeConfig.getFailedIncrementBreakThresholds();
//        AlertNoticeConfig alertNoticeConfig = tenantConfigurationManager.getAlertNoticeConfig(tenantId, dcId);
//        Integer alertThresholds = alertNoticeConfig.getFailedIncrementAlertThresholds();

        List<ErpAlarmRuleEntity> integrationStreamBreakConfigList = erpAlarmRuleManager.getAlarmTypeConfig(tenantId,
                dcId,
                ployDetailId,
                AlarmType.INTEGRATION_STREAM_BREAK);

        List<ErpAlarmRuleEntity> syncExceptionConfigList = erpAlarmRuleManager.getAlarmTypeConfig(tenantId,
                dcId,
                ployDetailId,
                AlarmType.SYNC_EXCEPTION);


        String incrementTopic = getIncrFailedTopic(tenantId, ployDetailId);
        //预估总增量+1
        Long num = redisDataSource.incrAndExpire(incrementTopic, 1L, syncFailedExpireSeconds, this.getClass().getSimpleName());

        AlarmRuleMatchModel integrationStreamBreakThresholdMathResult = erpAlarmRuleManager.getThresholdMathResult(integrationStreamBreakConfigList,
                num.intValue());
        AlarmRuleMatchModel syncExceptionThresholdMathResult = erpAlarmRuleManager.getThresholdMathResult(syncExceptionConfigList,
                num.intValue());

        Integer breakThresholds = integrationStreamBreakThresholdMathResult.getMatchThreshold();
        Integer alertThresholds = syncExceptionThresholdMathResult.getMatchThreshold();

        log.info("incrFailedSyncDataNumExecute,num={},alertThresholds={},breakThresholds={}", num,alertThresholds,breakThresholds);

        if (syncExceptionThresholdMathResult.getMatchThreshold()!=null || integrationStreamBreakThresholdMathResult.getMatchThreshold()!=null) {
            //预估达到告警阈值阈值，执行精确统计。之所以放到一个if里面,是兼容同时达到 告警和熔断条件。
            int exactCount = statSyncFailed(tenantId, dcId, ployDetailId, snapshot.getSourceObjectApiName(), snapshot.getDestObjectApiName());
            log.info("incrFailedSyncDataNumExecute,exactCount={},breakThresholds={}",exactCount,breakThresholds);
            if (breakThresholds!=null && exactCount >= breakThresholds.longValue()) {
                //达到熔断条件
                log.info("sync failed count attach break thresholds,{}", ployDetailId);
                //删除预估增量
                Long del = jedisCmd.del(incrementTopic);
                Long del2 = jedisCmd.del(idSetTopic);

                String content = i18NStringManager.getByEi2(I18NStringEnum.s706.getI18nKey(),
                        tenantId,
                        String.format(I18NStringEnum.s706.getI18nValue(), breakThresholds),
                        Lists.newArrayList(breakThresholds+""))+"\n";
                //触发熔断
                boolean disablePloyDetail = syncPloyManager.disablePloyDetail(tenantId, snapId, content);
                log.info("break ploy，tenantId：{}，snapId：{}，del:{}，del2:{}，ploy disable:{}",
                        tenantId, snapId, del, del2, disablePloyDetail);
                if (disablePloyDetail) {
                    //避免已经停用策略了，但是还不断触发通知
                    SyncPloyDetailEntity ployDetail = adminSyncPloyDetailManager.getEntryById(tenantId, ployDetailId);
                    String streamName = ployDetail.getIntegrationStreamName();
                    ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
                    String dcName = connectInfo.getDataCenterName();
                    String tenantInfo = i18NStringManager.getByEi2(I18NStringEnum.s703.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s703.getI18nValue(), connectInfo.getEnterpriseName(), tenantId),
                            Lists.newArrayList(connectInfo.getEnterpriseName(), tenantId))+ "\n";

                    String streamInfo = i18NStringManager.getByEi2(I18NStringEnum.s704.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s704.getI18nValue(), dcName,"\n", streamName),
                            Lists.newArrayList(dcName,"\n", streamName));

                    AlertMessageModel message = AlertMessageModel.builder()
                            .tenantId(tenantId)
                            .dataCenterId(dcId)
                            .ployDetailId(ployDetailId)
                            .errorMessage(content)
                            .streamName(streamName)
                            .streamInfo(streamInfo)
                            .tenantInfo(tenantInfo)
                            .title(i18NStringManager.getByEi(I18NStringEnum.s707,tenantId))
                            .build();
                    alert(message,
                            AlarmRuleType.GENERAL,
                            AlarmRuleType.GENERAL.getName(i18NStringManager,null,tenantId),
                            AlarmType.INTEGRATION_STREAM_BREAK,
                            integrationStreamBreakThresholdMathResult.getMatchEntity().getAlarmLevel());
                }
                //熔断了就不需要检查告警的了。
                return;
            }
            //未达到熔断条件，更新一下预估增量
            jedisCmd.setex(incrementTopic, syncFailedExpireSeconds, String.valueOf(exactCount));
            int mod = -1;
            if(alertThresholds!=null) {
                mod = exactCount % alertThresholds;
            }
            log.info("incrFailedSyncDataNumExecute,mod={},exactCount={},alertThresholds={}",mod,exactCount,alertThresholds);
            //只有启动了增量告警，并且达到增量告警阀值，才会触发增量告警
            if (mod == 0) {
                if(syncExceptionThresholdMathResult.getMatchEntity()!=null && syncExceptionThresholdMathResult.getMatchEntity().getAlarmRuleType()==AlarmRuleType.CUSTOM) {
                    log.info("incrFailedSyncDataNumExecute,send sync data failed custom alarm directly");
                    sendSyncDataFailedFirstAlert(tenantId,
                            dcId,
                            ployDetailId,
                            alertThresholds);
                    return;
                }
                //数据中心级同步数据失败告警
                AlertAggregationEntity alertAggregationEntity = alertAggregationDao.getData(tenantId,
                        dcId,
                        null,
                        AlertAggregationType.SYNC_DATA_FAILED_ALERT);
                if(alertAggregationEntity==null) {
                    //首次触发告警，先设置告警标识
                    alertAggregationDao.insert(tenantId,
                            dcId,
                            ployDetailId,
                            AlertAggregationType.SYNC_DATA_FAILED_ALERT,
                            AlarmLevel.IMPORTANT,
                            System.currentTimeMillis(),
                            1,
                            false,
                            null,
                            null,
                            null);
                    sendSyncDataFailedFirstAlert(tenantId,
                            dcId,
                            ployDetailId,
                            alertThresholds);
                } else {
                    //数据中心级同步数据失败告警
                    AlertAggregationEntity alertAggregationEntity2 = alertAggregationDao.getData(tenantId,
                            dcId,
                            ployDetailId,
                            AlertAggregationType.SYNC_DATA_FAILED_ALERT);
                    if(alertAggregationEntity2!=null) {
                        //如果已经触发过告警
                        //执行聚合告警逻辑，告警次数+1
                        alertAggregationEntity.setCount(alertAggregationEntity.getCount()+1);
                        alertAggregationEntity.setUpdateTime(new Date());
                        alertAggregationDao.replace(alertAggregationEntity);
                    } else {
                        //当前集成流首次触发告警，先设置告警标识
                        alertAggregationDao.insert(tenantId,
                                dcId,
                                ployDetailId,
                                AlertAggregationType.SYNC_DATA_FAILED_ALERT,
                                AlarmLevel.IMPORTANT,
                                System.currentTimeMillis(),
                                1,
                                false,
                                null,
                                null,
                                null);
                    }

                    //同步失败数据达到告警阀值，生成告警记录，用于统计告警中的集成流
                    ErpAlarmRuleEntity erpAlarmRuleEntity = syncExceptionThresholdMathResult.getMatchEntity();
                    dataIntegrationNotificationManager.insert(tenantId,
                            dcId,
                            Lists.newArrayList(ployDetailId),
                            erpAlarmRuleEntity.getAlarmRuleType(),
                            erpAlarmRuleEntity.getAlarmRuleType().getName(i18NStringManager, null, tenantId),
                            erpAlarmRuleEntity.getAlarmType(),
                            erpAlarmRuleEntity.getAlarmLevel(),
                            i18NStringManager.get(I18NStringEnum.s2274, null, tenantId) + erpAlarmRuleEntity.getThreshold(),
                            erpAlarmRuleEntity.getUserIdList2(),
                            erpAlarmRuleEntity.getNotifyType()
                    );
                }
            }
        }
    }

    /**
     * 发送同步数据失败 首次 告警
     * @param tenantId
     * @param dcId
     * @param ployDetailId
     * @param alertThresholds
     */
    public void sendSyncDataFailedFirstAlert(String tenantId,
                                             String dcId,
                                             String ployDetailId,
                                             Integer alertThresholds) {
        String content = i18NStringManager.getByEi2(I18NStringEnum.s701.getI18nKey(),
                tenantId,
                String.format(I18NStringEnum.s701.getI18nValue(), alertThresholds),
                Lists.newArrayList(alertThresholds+""))+"\n";

        SyncPloyDetailEntity ployDetail = adminSyncPloyDetailManager.getEntryById(tenantId, ployDetailId);
        String streamName = ployDetail.getIntegrationStreamName();
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
        String dcName = connectInfo.getDataCenterName();
        String tenantInfo = i18NStringManager.getByEi2(I18NStringEnum.s703.getI18nKey(),
                tenantId,
                String.format(I18NStringEnum.s703.getI18nValue(), connectInfo.getEnterpriseName(), tenantId),
                Lists.newArrayList(connectInfo.getEnterpriseName(), tenantId))+ "\n";

        String streamInfo = i18NStringManager.getByEi2(I18NStringEnum.s704.getI18nKey(),
                tenantId,
                String.format(I18NStringEnum.s704.getI18nValue(), dcName,"\n", streamName),
                Lists.newArrayList(dcName,"\n", streamName));

        AlertMessageModel message = AlertMessageModel.builder()
                .tenantId(tenantId)
                .dataCenterId(dcId)
                .ployDetailId(ployDetailId)
                .errorMessage(content)
                .streamName(streamName)
                .streamInfo(streamInfo)
                .tenantInfo(tenantInfo)
                .title(getAlertTitle(tenantId,AlertType.ALERT))
                .build();
        alert(message,
                AlarmRuleType.GENERAL,
                AlarmRuleType.GENERAL.getName(i18NStringManager,null,tenantId),
                AlarmType.SYNC_EXCEPTION,
                AlarmLevel.IMPORTANT);
        log.info("send sync data failed fisrt alert,tenantId:{}, dcId:{}, ployDetailId:{}, message:{}", tenantId, dcId, ployDetailId, message);
    }

    /**
     * 发送同步数据失败告警恢复通知，按数据中心进行聚合
     * @param tenantDcDataMap
     */
    public void sendSyncDataFailedAlertRecover(Map<String,List<SyncDataFailedModel>> tenantDcDataMap) {
        for(String key : tenantDcDataMap.keySet()) {
            List<SyncDataFailedModel> syncSuccessDataList = tenantDcDataMap.get(key);
            String tenantId = null;
            String dcId = null;
            String tenantInfo = null;
            String dcName = null;
            String streamInfo = null;
            String streamName = null;

            int totalSize = 0;
            for(SyncDataFailedModel entity : syncSuccessDataList) {
                tenantId = entity.getTenantId();
                dcId = entity.getDataCenterId();
                String ployDetailId = entity.getPloyDetailId();

                SyncPloyDetailEntity ployDetail = adminSyncPloyDetailManager.getEntryById(tenantId, ployDetailId);
                //如果集成流已停用，不发送告警恢复通知
                if(ployDetail.getStatus()== SyncPloyDetailStatusEnum.DISABLE.getStatus()) continue;

                streamName = ployDetail.getIntegrationStreamName();
                ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
                dcName = connectInfo.getDataCenterName();
                tenantInfo = i18NStringManager.getByEi2(I18NStringEnum.s703.getI18nKey(),
                        tenantId,
                        String.format(I18NStringEnum.s703.getI18nValue(), connectInfo.getEnterpriseName(), tenantId),
                        Lists.newArrayList(connectInfo.getEnterpriseName(), tenantId))+ "\n";
                if(StringUtils.isEmpty(streamInfo)) {
                    streamInfo = i18NStringManager.getByEi2(I18NStringEnum.s704.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s704.getI18nValue(), dcName,"\n", streamName),
                            Lists.newArrayList(dcName,"\n", streamName));
                } else {
                    streamInfo += "," + streamName;
                }
                totalSize += entity.getCount();
            }

            if(totalSize==0) continue;

            String strTotalSize = null;
            if(totalSize > 100 && totalSize < 500) {
                strTotalSize = "100+";
            } else if(totalSize > 500 && totalSize < 1000) {
                strTotalSize = "500+";
            } else if(totalSize > 1000 && totalSize < 10000) {
                strTotalSize = "1000+";
            } else if(totalSize > 10000) {
                strTotalSize = "10000+";
            } else {
                strTotalSize = totalSize + "";
            }

            //按数据中心聚合告警恢复信息
            String content = i18NStringManager.getByEi2(I18NStringEnum.s708.getI18nKey(),
                    tenantId,
                    String.format(I18NStringEnum.s708.getI18nValue(), strTotalSize),
                    Lists.newArrayList(strTotalSize+""))+"\n";

            AlertMessageModel message = AlertMessageModel.builder()
                    .tenantId(tenantId)
                    .dataCenterId(dcId)
                    .ployDetailId(null)
                    .errorMessage(content)
                    .streamName(streamName)
                    .streamInfo(streamInfo)
                    .tenantInfo(tenantInfo)
                    .title(getAlertTitle(tenantId,AlertType.RECOVER))
                    .build();

            alert(message,
                    AlarmRuleType.GENERAL,
                    AlarmRuleType.GENERAL.getName(i18NStringManager,null,tenantId),
                    AlarmType.SYNC_EXCEPTION,
                    AlarmLevel.IMPORTANT);
            log.info("send sync data failed recover,tenantId:{}, dcId:{}, message:{}", tenantId, dcId, message);
        }
    }

    /**
     * 发送同步数据失败告警聚合通知，按数据中心进行聚合
     * @param tenantDcDataMap
     */
    public void sendSyncDataFailedAlertAggregation(Map<String,List<AlertAggregationEntity>> tenantDcDataMap) {
        for(String key : tenantDcDataMap.keySet()) {
            List<AlertAggregationEntity> syncSuccessDataList = tenantDcDataMap.get(key);
            String tenantId = null;
            String dcId = null;
            String tenantInfo = null;
            String dcName = null;
            String streamInfo = null;
            String streamName = null;

            int totalSize = 0;
            for(AlertAggregationEntity entity : syncSuccessDataList) {
                tenantId = entity.getTenantId();
                dcId = entity.getDataCenterId();
                String ployDetailId = entity.getPloyDetailId();

                SyncPloyDetailEntity ployDetail = adminSyncPloyDetailManager.getEntryById(tenantId, ployDetailId);
                //如果集成流已停用，不发送聚合告警通知
                if(ployDetail.getStatus()== SyncPloyDetailStatusEnum.DISABLE.getStatus()) continue;

                streamName = ployDetail.getIntegrationStreamName();
                ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
                dcName = connectInfo.getDataCenterName();
                tenantInfo = i18NStringManager.getByEi2(I18NStringEnum.s703.getI18nKey(),
                        tenantId,
                        String.format(I18NStringEnum.s703.getI18nValue(), connectInfo.getEnterpriseName(), tenantId),
                        Lists.newArrayList(connectInfo.getEnterpriseName(), tenantId))+ "\n";
                if(StringUtils.isEmpty(streamInfo)) {
                    streamInfo = i18NStringManager.getByEi2(I18NStringEnum.s704.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s704.getI18nValue(), dcName,"\n", streamName),
                            Lists.newArrayList(dcName,"\n", streamName));
                } else {
                    streamInfo += "," + streamName;
                }
                totalSize +=entity.getCount();
            }

            if(totalSize==0) continue;

//            AlertNoticeConfig alertNoticeConfig = tenantConfigurationManager.getAlertNoticeConfig(tenantId, dcId);
//            Integer alertThresholds = alertNoticeConfig.getFailedIncrementAlertThresholds();

            ErpAlarmRuleEntity syncExceptionEntity = erpAlarmRuleManager.getAlarmTypeConfig2(tenantId,
                    dcId,
                    AlarmRuleType.GENERAL,
                    AlarmType.SYNC_EXCEPTION);
            Integer alertThresholds = syncExceptionEntity.getThreshold();

            //按数据中心聚合告警信息
            String content = i18NStringManager.getByEi2(I18NStringEnum.s706.getI18nKey(),
                    tenantId,
                    String.format(I18NStringEnum.s706.getI18nValue(), alertThresholds * totalSize),
                    Lists.newArrayList(alertThresholds * totalSize+""))+"\n";

            AlertMessageModel message = AlertMessageModel.builder()
                    .tenantId(tenantId)
                    .dataCenterId(dcId)
                    .ployDetailId(null)
                    .errorMessage(content)
                    .streamName(streamName)
                    .streamInfo(streamInfo)
                    .tenantInfo(tenantInfo)
                    .title(getAlertTitle(tenantId,AlertType.AGGREGATE_ALERT))
                    .build();

            alert(message,
                    AlarmRuleType.GENERAL,
                    AlarmRuleType.GENERAL.getName(i18NStringManager,null,tenantId),
                    AlarmType.SYNC_EXCEPTION,
                    AlarmLevel.IMPORTANT);
            log.info("send sync data failed aggregation,tenantId:{}, dcId:{}, message:{}", tenantId, dcId, message);
        }
    }

    /**
     * 把告警统计数据发送到客群
     */
    public void sendDailyAlarmStatistics2CustomerGroup(String tenantId, Map<AlarmLevel, List<AlertAggregationEntity>> alertDataMap) {
        log.info("AlertAndBreakManager.sendDailyAlarmStatistics2CustomerGroup,tenantId={},alertDataMap={}",tenantId,alertDataMap);

        int urgentIntegrationStreamCount = alertAggregationDao.getTenantDcPloyDetailDataMap(alertDataMap.get(AlarmLevel.URGENT)).size();

        int importantIntegrationStreamCount = alertAggregationDao.getTenantDcPloyDetailDataMap(alertDataMap.get(AlarmLevel.IMPORTANT)).size();

        int generalIntegrationStreamCount = alertAggregationDao.getTenantDcPloyDetailDataMap(alertDataMap.get(AlarmLevel.GENERAL)).size();

        log.info("AlertAndBreakManager.sendDailyAlarmStatistics2CustomerGroup,urgentCount={},importantCount={},generalCount={}",
                urgentIntegrationStreamCount,importantIntegrationStreamCount,generalIntegrationStreamCount);

        StringBuilder sb = new StringBuilder();
        sb.append(i18NStringManager.getByEi(I18NStringEnum.s2265,tenantId)+"\n");

        String defaultValue = String.format(I18NStringEnum.s2266.getI18nValue(),
                urgentIntegrationStreamCount,importantIntegrationStreamCount,generalIntegrationStreamCount);
        sb.append(i18NStringManager.getByEi2(I18NStringEnum.s2266.getI18nKey(),
                tenantId,
                defaultValue,
                Lists.newArrayList(urgentIntegrationStreamCount+"",importantIntegrationStreamCount+"",generalIntegrationStreamCount+""))+"\n");
        sb.append(i18NStringManager.getByEi(I18NStringEnum.s2267,tenantId));

        SendAdminNoticeArg arg = SendAdminNoticeArg.builder()
                .tenantId(tenantId)
                .msgTitle(i18NStringManager.getByEi(I18NStringEnum.s700,tenantId))
                .msg(sb.toString())
                .build();
        //发送告警统计通知到 客群
        notificationService.sendCustomerRoomNotice(arg, AlarmLevel.URGENT);
        log.info("AlertAndBreakManager.sendDailyAlarmStatistics2CustomerGroup,arg:{}", arg);
    }

    private String getAlertTitle(String tenantId,AlertType alertType) {
        String title = null;
        switch (alertType) {
            case ALERT:
                title = i18NStringManager.getByEi(I18NStringEnum.s700,tenantId);
                break;
            case AGGREGATE_ALERT:
                title = i18NStringManager.getByEi(I18NStringEnum.s709,tenantId);
                break;
            case RECOVER:
                title = i18NStringManager.getByEi(I18NStringEnum.s710,tenantId);
                break;
        }
        return title;
    }

    private String getIdSetTopic(String tenantId, String ployDetailId) {
        String name = joiner.join(tenantId, ployDetailId);
        String idSetTopic = String.format(CommonConstant.REDIS_KEY_FAILED_SYNC_ID_SET, name);
        return idSetTopic;
    }


    public String getIncrFailedTopic(String tenantId, String ployDetailId) {
        String name = joiner.join(tenantId, ployDetailId);
        String incrementTopic = String.format(CommonConstant.REDIS_KEY_FAILED_SYNC_INCREMENT, name);
        return incrementTopic;
    }

    public int statSyncFailed(String tenantId, String dcId, String ployDetailId, String srcObj, String destObj) {
        SyncFailedStat syncFailedStat = readStat(tenantId, dcId, ployDetailId);
        log.info("AlertAndBreakManager.statSyncFailed,syncFailedStat={}",syncFailedStat);
        if (syncFailedStat == null) {
            //初始化失败
            return 0;
        }
        //主数据
        SyncObjectAndTenantMappingData syncObjectAndTenantMappingData = new SyncObjectAndTenantMappingData();
        syncObjectAndTenantMappingData.setSourceTenantId(tenantId);
        syncObjectAndTenantMappingData.setSourceObjectApiName(srcObj);
        syncObjectAndTenantMappingData.setDestTenantId(tenantId);
        syncObjectAndTenantMappingData.setDestObjectApiName(destObj);
        int count;
        try {
            count = syncDataMappingsDao.setTenantId(tenantId)
                    .countSyncFailedAfterTime(tenantId, syncObjectAndTenantMappingData, syncFailedStat.getLastBreakTime());
        } catch (Exception e) {
            log.error("count sync fail after time failed", e);
            return 0;
        }
        log.info("AlertAndBreakManager.statSyncFailed,tenantId={},dcId={},ployDetailId={},count={}",tenantId,dcId,ployDetailId,count);
        //更新count
        syncFailedStatDao.updateLastCount(tenantId, dcId, ployDetailId, count, System.currentTimeMillis());
        log.info("AlertAndBreakManager.statSyncFailed,count={}, syncObjectAndTenantMappingData={}", count, syncObjectAndTenantMappingData);
        return count;
    }

    @SneakyThrows
    private SyncFailedStat readStat(String tenantId, String dcId, String ployDetailId) {
        SyncFailedStat syncFailedStat = syncFailedStatDao.getSyncFailedStat(tenantId, dcId, ployDetailId);
        if (syncFailedStat == null) {
            //初始的熔断时间为当前时间
            syncFailedStat = new SyncFailedStat();
            syncFailedStat.setLastBreakTime(System.currentTimeMillis());
            //插入数据
            syncFailedStatDao.replace(tenantId, dcId, ployDetailId, syncFailedStat);
        }
        log.info("AlertAndBreakManager.readStat,syncFailedStat={}", syncFailedStat);
        return syncFailedStat;
    }

}
