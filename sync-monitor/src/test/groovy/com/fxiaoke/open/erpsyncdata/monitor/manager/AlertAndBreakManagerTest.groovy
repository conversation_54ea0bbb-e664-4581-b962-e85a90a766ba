//package com.fxiaoke.open.erpsyncdata.monitor.manager
//
//import cn.hutool.core.lang.UUID
//import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant
//import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity
//import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao
//import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource
//import com.fxiaoke.open.erpsyncdata.monitor.BaseSpockTest
//import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg
//import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum
//import com.fxiaoke.open.erpsyncdata.preprocess.result.base.BaseResult
//import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
//import com.fxiaoke.otherrestapi.eservice.common.util.UUIDUtil
//import com.google.common.base.Joiner
//import org.junit.Ignore
//import org.junit.Test
//import org.springframework.beans.factory.annotation.Autowired
//import spock.lang.Specification
//
///**
// *
// * <AUTHOR> (^_−)☆
// * @date 2023/2/3
// */
//@Ignore
//class AlertAndBreakManagerTest extends BaseSpockTest {
//
//    @Autowired
//    private AlertAndBreakManager ployBreakManager;
//    @Autowired
//    private SyncDataFixDao syncDataFixDao;
//    @Autowired
//    private RedisDataSource redisDataSource;
//    private final Joiner joiner = Joiner.on("-").skipNulls();
//
//    @Test
//    void test() {
////        String name = joiner.join("88466", "39f118167af04033917dd01d8bb3f57d")
////        String idSetTopic = String.format(CommonConstant.REDIS_KEY_FAILED_SYNC_ID_SET, name)
////        def value = redisDataSource.get(this.getClass().getSimpleName()).zrange(idSetTopic,0,100)
//
//        def zadd = redisDataSource.get(this.getClass().getSimpleName()).zadd("kgdwbb", 1, "kgd1")
//        println(zadd)
//
//        def zadd2 = redisDataSource.get(this.getClass().getSimpleName()).zadd("kgdwbb", 2, "kgd2")
//        println(zadd2)
//
//        def zcard = redisDataSource.get(this.getClass().getSimpleName()).zcard("kgdwbb")
//        println(zcard)
//
//        def del = redisDataSource.get(this.getClass().getSimpleName()).del("kgdwbb")
//
//        def zrange = redisDataSource.get(this.getClass().getSimpleName()).zrange("kgdwbb",0,10)
//        println(zrange)
//
//        def zrem = redisDataSource.get(this.getClass().getSimpleName()).zrem("kgdwbb","kgd1")
//        println(zrem)
//
//        def zrange2 = redisDataSource.get(this.getClass().getSimpleName()).zrange("kgdwbb",0,10)
//        println(zrange2)
//    }
//
//    @Test
//    void statSyncFailed() {
//        def count = ployBreakManager.statSyncFailed("88466",
//                "6436278b3dcc6b0001e76652",
//                "39f118167af04033917dd01d8bb3f57d",
//                "BD_MATERIAL.BillHead",
//                "ProductObj")
//        println(count)
//    }
//
//    @Test
//    public void handelPollingErpResult() {
////        TimeFilterArg arg = new TimeFilterArg();
////        arg.setTenantId("83952");
////        arg.setStartTime(0L);
////        arg.setEndTime(1660809905000L);
////        arg.setObjAPIName("LargeBatchObj_1g8vh6rsm");
////        arg.setSnapshotId("3451dd88ac3041e8be59883a767101d7");
////        arg.setOperationType(2);
////        ployBreakManager.handelPollingErpResult(arg,true);
////        for (int i = 0; i < 9; i++) {
////            ployBreakManager.handelPollingErpResult(arg,false);
////        }
////        ployBreakManager.handelPollingErpResult(arg,true);
////        for (int i = 0; i < 10; i++) {
////            ployBreakManager.handelPollingErpResult(arg,false);
////        }
//    }
//
//    @Test
//    public void handelPollingTempFailed() {
//        TimeFilterArg arg = new TimeFilterArg();
//        arg.setTenantId("83952");
//        arg.setStartTime(0L);
//        arg.setEndTime(1660809905000L);
//        arg.setObjAPIName("LargeBatchObj_1g8vh6rsm");
//        arg.setSnapshotId("3451dd88ac3041e8be59883a767101d7");
//        ployBreakManager.handelPollingTempFailed(arg, Result.newError("测试轮询temp异常"));
//    }
//
//    @Test
//    public void incrFailedSyncDataNum() {
//        testIncrFailed();
//    }
//
//    private void testIncrFailed() {
//        SyncDataEntity syncData = new SyncDataEntity();
//        String testId = "testId";
//        syncData.setId(testId);
//        syncData.setTenantId("81243");
//        syncData.setSourceObjectApiName("AR_RECEIVEBILL.BillHead");
//        syncData.setSyncPloyDetailSnapshotId("ef861a0ab320443789e55c60c4614cad");
//        syncDataFixDao.insertCache(syncData);
//        syncData.setSourceDataId(testId);
//        for (int i = 0; i < 300; i++) {
//            //相同id
//            ployBreakManager.incrFailedSyncDataNum("81243", syncData);
//        }
//        for (int i = 0; i < 301; i++) {
//            //不同id
//            syncData.setSourceDataId(testId+i);
//            ployBreakManager.incrFailedSyncDataNum("83952", syncData);
//        }
//        for (int i = 0; i < 2001; i++) {
//            //不同id
//            syncData.setSourceDataId(testId+i);
//            ployBreakManager.incrFailedSyncDataNum("83952", syncData);
//        }
//    }
//
//    @Test
//    public void getBreakFailedSyncDataNum() {
//
//        SyncDataEntity syncData = new SyncDataEntity();
//        String testId = "testId";
//        for (int i = 0; i < 300; i++) {
//            //相同id
//            syncData.setId(testId);
//            syncData.setTenantId("81243");
//            syncData.setSourceObjectApiName("AR_RECEIVEBILL.BillHead");
//            syncData.setSyncPloyDetailSnapshotId("ef861a0ab320443789e55c60c4614cad");
//            syncDataFixDao.insertCache(syncData);
//            syncData.setSourceDataId(testId+i+ UUID.fastUUID().toString());
//            ployBreakManager.incrFailedSyncDataNum("81243", syncData);
//        }
//
//
//
//
////        TimeFilterArg timeFilterArg=new TimeFilterArg();
////        timeFilterArg.setObjAPIName("AR_RECEIVEBILL.BillHead");
////        timeFilterArg.setTenantId("81243");
////        timeFilterArg.setSnapshotId("ef861a0ab320443789e55c60c4614cad")
////        timeFilterArg.setOperationType(2)
////        BaseResult baseResult=new BaseResult() {
////            @Override
////            String getErrCode() {
////                return super.getErrCode()
////            }
////        }
////        baseResult.setErrMsg("错误数据")
////        baseResult.setErrCode(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT.errCode);
////        for (int i = 0; i < 300; i++) {
////            ployBreakManager.handelPollingErpResult(timeFilterArg,baseResult);
////        }
//    }
//}
