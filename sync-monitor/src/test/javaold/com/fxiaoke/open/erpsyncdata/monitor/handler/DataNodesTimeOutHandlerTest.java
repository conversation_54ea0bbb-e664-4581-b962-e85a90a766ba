package com.fxiaoke.open.erpsyncdata.monitor.handler;

import com.fxiaoke.open.erpsyncdata.monitor.BaseTest;
import com.google.common.collect.Sets;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 11:41 2023/2/10
 * @Desc:
 */
@Ignore
public class DataNodesTimeOutHandlerTest extends BaseTest {

    @Autowired
    private DataNodesTimeOutHandler dataNodesTimeOutHandler;

    @Test
    public void doProcessTimeoutNodes() {
        dataNodesTimeOutHandler.doProcessTimeoutNodes("81243", null, 1000 * 60L, System.currentTimeMillis(), Sets.newHashSet());
        System.out.println("");
    }
}