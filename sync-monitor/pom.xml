 <project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.facishare.open</groupId>
        <artifactId>fs-erp-sync-data</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>sync-monitor</artifactId>
    <packaging>war</packaging>
    <name>sync-monitor</name>
    <url>http://maven.apache.org</url>
    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${spring-framework.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>sync-monitor-jar</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>sync-monitor</finalName>
    </build>
</project>
